# Zephyr Weather Service Makefile
# Go-based weather monitoring service with Protocol Buffers and ConnectRPC

# Variables
GO_VERSION := 1.24
BINARY_NAME := zephyr
API_BINARY := zephyr-api
WORKER_BINARY := zephyr-worker
BUILD_DIR := build
PROTO_DIR := api/proto
GEN_DIR := api/gen
CMD_DIR := cmd

# Build information
VERSION ?= $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
BUILD_TIME := $(shell date -u +"%Y-%m-%dT%H:%M:%SZ")

# Go build flags
LDFLAGS := -ldflags "-X main.Version=$(VERSION) -X main.Commit=$(COMMIT) -X main.BuildTime=$(BUILD_TIME)"
BUILD_FLAGS := -v $(LDFLAGS)

# Tool versions
BUF_VERSION := latest
GOLANGCI_LINT_VERSION := latest
NANCY_VERSION := latest
GOVULNCHECK_VERSION := latest
STATICCHECK_VERSION := latest

# Colors for output - using tput for reliable cross-platform colors
RED := $(shell tput setaf 1 2>/dev/null)
GREEN := $(shell tput setaf 2 2>/dev/null)
YELLOW := $(shell tput setaf 3 2>/dev/null)
BLUE := $(shell tput setaf 4 2>/dev/null)
PURPLE := $(shell tput setaf 5 2>/dev/null)
CYAN := $(shell tput setaf 6 2>/dev/null)
NC := $(shell tput sgr0 2>/dev/null) # No Color

.PHONY: help
help: ## Show this help message
	@echo "$(CYAN)Zephyr Weather Service$(NC)"
	@echo "Available commands:"
	@awk 'BEGIN {FS = ":.*##"} /^[a-zA-Z_-]+:.*##/ {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# ==============================================================================
# Development Setup
# ==============================================================================

.PHONY: setup
setup: install-tools deps generate ## Complete development environment setup
	@echo "$(GREEN)✅ Development environment setup complete$(NC)"

.PHONY: install-tools
install-tools: ## Install required development tools
	@echo "$(BLUE)📦 Installing development tools...$(NC)"
	@echo "  Installing buf..."
	@go install github.com/bufbuild/buf/cmd/buf@$(BUF_VERSION)
	@echo "  Installing golangci-lint..."
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@$(GOLANGCI_LINT_VERSION)
	@echo "  Installing govulncheck..."
	@go install golang.org/x/vuln/cmd/govulncheck@$(GOVULNCHECK_VERSION)
	@echo "  Installing staticcheck..."
	@go install honnef.co/go/tools/cmd/staticcheck@$(STATICCHECK_VERSION)
	@echo "$(GREEN)✅ Tools installed$(NC)"

.PHONY: deps
deps: ## Download and tidy Go dependencies
	@echo "$(BLUE)📦 Downloading dependencies...$(NC)"
	@go mod download
	@go mod tidy
	@go mod verify
	@echo "$(GREEN)✅ Dependencies updated and verified$(NC)"

# ==============================================================================
# Protocol Buffers
# ==============================================================================

.PHONY: proto-lint
proto-lint: ## Lint Protocol Buffer files
	@echo "$(BLUE)🔍 Linting Protocol Buffers...$(NC)"
	@buf lint

.PHONY: proto-format
proto-format: ## Format Protocol Buffer files
	@echo "$(BLUE)✨ Formatting Protocol Buffers...$(NC)"
	@buf format -w

.PHONY: proto-breaking
proto-breaking: ## Check for breaking changes in Protocol Buffers
	@echo "$(BLUE)🔍 Checking for breaking changes...$(NC)"
	@buf breaking --against '.git#branch=main'

.PHONY: generate
generate: proto-lint ## Generate Go code from Protocol Buffers
	@echo "$(BLUE)🔧 Generating Protocol Buffer code...$(NC)"
	@buf generate
	@go mod tidy
	@go fmt ./api/gen/...
	@echo "$(GREEN)✅ Code generation complete$(NC)"

.PHONY: proto-clean
proto-clean: ## Clean generated Protocol Buffer files
	@echo "$(YELLOW)🧹 Cleaning generated files...$(NC)"
	@rm -rf $(GEN_DIR)
	@echo "$(GREEN)✅ Generated files cleaned$(NC)"

# ==============================================================================
# Build
# ==============================================================================

.PHONY: build
build: build-api build-worker ## Build all binaries

.PHONY: build-api
build-api: generate ## Build API server
	@echo "$(BLUE)🔨 Building API server...$(NC)"
	@mkdir -p $(BUILD_DIR)
	@go build $(BUILD_FLAGS) -o $(BUILD_DIR)/$(API_BINARY) ./$(CMD_DIR)/api
	@echo "$(GREEN)✅ API server built: $(BUILD_DIR)/$(API_BINARY)$(NC)"

.PHONY: build-worker
build-worker: generate ## Build worker binary
	@echo "$(BLUE)🔨 Building worker...$(NC)"
	@mkdir -p $(BUILD_DIR)
	@go build $(BUILD_FLAGS) -o $(BUILD_DIR)/$(WORKER_BINARY) ./$(CMD_DIR)/worker
	@echo "$(GREEN)✅ Worker built: $(BUILD_DIR)/$(WORKER_BINARY)$(NC)"

.PHONY: build-debug
build-debug: ## Build with debug symbols
	@echo "$(BLUE)🔨 Building with debug symbols...$(NC)"
	@mkdir -p $(BUILD_DIR)
	@go build -gcflags="all=-N -l" -o $(BUILD_DIR)/$(API_BINARY)-debug ./$(CMD_DIR)/api
	@echo "$(GREEN)✅ Debug build complete$(NC)"

.PHONY: build-all
build-all: generate build cross-compile ## Build all targets including cross-compilation

.PHONY: install
install: build-api ## Install API binary to GOPATH/bin
	@echo "$(BLUE)📦 Installing binary...$(NC)"
	@go install ./$(CMD_DIR)/api
	@echo "$(GREEN)✅ Binary installed to $(shell go env GOPATH)/bin/$(NC)"

# ==============================================================================
# Testing
# ==============================================================================

.PHONY: test
test: ## Run all tests
	@echo "$(BLUE)🧪 Running tests...$(NC)"
	@go fmt ./...
	@go vet ./...
	@go test -v -race -coverprofile=coverage.out ./...
	@echo "$(GREEN)✅ Tests completed$(NC)"

.PHONY: test-coverage
test-coverage: test ## Run tests with coverage report
	@echo "$(BLUE)📊 Generating coverage report...$(NC)"
	@go tool cover -html=coverage.out -o coverage.html
	@echo "$(GREEN)✅ Coverage report: coverage.html$(NC)"

.PHONY: test-unit
test-unit: ## Run unit tests only
	@echo "$(BLUE)🧪 Running unit tests...$(NC)"
	@go test -v -race -short ./...

.PHONY: test-verbose
test-verbose: ## Run tests with verbose output
	@echo "$(BLUE)🧪 Running tests with verbose output...$(NC)"
	@go test -v -race -coverprofile=coverage.out -covermode=atomic ./...

.PHONY: test-watch
test-watch: ## Run tests in watch mode (requires fswatch)
	@echo "$(BLUE)👀 Running tests in watch mode...$(NC)"
	@if ! command -v fswatch >/dev/null 2>&1; then \
		echo "$(RED)❌ fswatch not found. Install with: brew install fswatch$(NC)"; \
		exit 1; \
	fi
	@fswatch -o . -e ".*" -i "\\.go$$" | xargs -n1 -I{} make test-unit
test-bench: ## Run benchmark tests
	@echo "$(BLUE)⚡ Running benchmarks...$(NC)"
	@go test -bench=. -benchmem ./...

.PHONY: test-integration
test-integration: ## Run integration tests
	@echo "$(BLUE)🔗 Running integration tests...$(NC)"
	@go test -v -tags=integration ./...

# ==============================================================================
# Code Quality
# ==============================================================================

.PHONY: lint
lint: ## Run linter
	@echo "$(BLUE)🔍 Running linter...$(NC)"
	@golangci-lint run ./...
	@echo "$(GREEN)✅ Linting complete$(NC)"

.PHONY: lint-fix
lint-fix: ## Auto-fix linting issues where possible
	@echo "$(BLUE)🔧 Auto-fixing linting issues...$(NC)"
	@golangci-lint run --fix ./...

.PHONY: staticcheck
staticcheck: ## Run staticcheck
	@echo "$(BLUE)🔍 Running staticcheck...$(NC)"
	@staticcheck ./...
	@echo "$(GREEN)✅ Staticcheck complete$(NC)"

.PHONY: format
format: ## Format Go code
	@echo "$(BLUE)✨ Formatting Go code...$(NC)"
	@go fmt ./...
	@echo "$(GREEN)✅ Code formatted$(NC)"

.PHONY: vet
vet: ## Run go vet
	@echo "$(BLUE)🔍 Running go vet...$(NC)"
	@go vet ./...
	@echo "$(GREEN)✅ Vet check complete$(NC)"

.PHONY: check
check: format vet lint staticcheck test ## Run all code quality checks
	@echo "$(GREEN)✅ All quality checks passed$(NC)"

.PHONY: fix
fix: ## Auto-fix linting issues where possible
	@echo "$(BLUE)🔧 Auto-fixing issues...$(NC)"
	@go fmt ./...
	@go mod tidy
	@golangci-lint run --fix ./...
	@echo "$(GREEN)✅ Auto-fix complete$(NC)"

# ==============================================================================
# Security
# ==============================================================================

.PHONY: security
security: vulncheck deps-audit ## Run all security checks

.PHONY: vulncheck
vulncheck: ## Check for known vulnerabilities
	@echo "$(BLUE)🔒 Checking for vulnerabilities...$(NC)"
	@govulncheck ./...
	@echo "$(GREEN)✅ Vulnerability check complete$(NC)"

.PHONY: deps-audit
deps-audit: ## Audit dependencies for security issues
	@echo "$(BLUE)🔍 Auditing dependencies...$(NC)"
	@go list -json -m all | nancy sleuth || echo "$(YELLOW)⚠️  nancy not installed, skipping dependency audit$(NC)"
	@echo "$(GREEN)✅ Dependency audit complete$(NC)"

# ==============================================================================
# Development Server
# ==============================================================================

.PHONY: run-api
run-api: build-api ## Run API server
	@echo "$(BLUE)🚀 Starting API server...$(NC)"
	@./$(BUILD_DIR)/$(API_BINARY)

.PHONY: run-worker
run-worker: build-worker ## Run worker
	@echo "$(BLUE)🚀 Starting worker...$(NC)"
	@./$(BUILD_DIR)/$(WORKER_BINARY)

.PHONY: dev
dev: ## Run API server in development mode with live reload
	@echo "$(BLUE)🔄 Starting development server...$(NC)"
	@go run ./$(CMD_DIR)/api

.PHONY: debug
debug: build-debug ## Run API server with delve debugger
	@echo "$(BLUE)🐛 Starting debug server...$(NC)"
	@dlv exec $(BUILD_DIR)/$(API_BINARY)-debug

# ==============================================================================
# Docker
# ==============================================================================

.PHONY: docker-build
docker-build: ## Build Docker image
	@echo "$(BLUE)🐳 Building Docker image...$(NC)"
	@docker build -t zephyr:$(VERSION) .
	@docker build -t zephyr:latest .
	@echo "$(GREEN)✅ Docker image built$(NC)"

.PHONY: docker-run
docker-run: ## Run Docker container
	@echo "$(BLUE)🐳 Running Docker container...$(NC)"
	@docker run -p 8080:8080 zephyr:latest

.PHONY: docker-compose-up
docker-compose-up: ## Start all services with docker-compose
	@echo "$(BLUE)🐳 Starting services with docker-compose...$(NC)"
	@docker-compose up -d

.PHONY: docker-compose-down
docker-compose-down: ## Stop all services
	@echo "$(BLUE)🐳 Stopping services...$(NC)"
	@docker-compose down

.PHONY: docker-compose-logs
docker-compose-logs: ## View docker-compose logs
	@docker-compose logs -f

# ==============================================================================
# Database
# ==============================================================================

.PHONY: redis-start
redis-start: ## Start Redis locally
	@echo "$(BLUE)🔴 Starting Redis...$(NC)"
	@docker run -d --name zephyr-redis -p 6379:6379 redis:7-alpine
	@echo "$(GREEN)✅ Redis started on port 6379$(NC)"

.PHONY: influx-start
influx-start: ## Start InfluxDB locally
	@echo "$(BLUE)📈 Starting InfluxDB...$(NC)"
	@docker run -d --name zephyr-influx -p 8086:8086 \
		-e DOCKER_INFLUXDB_INIT_MODE=setup \
		-e DOCKER_INFLUXDB_INIT_USERNAME=admin \
		-e DOCKER_INFLUXDB_INIT_PASSWORD=password123 \
		-e DOCKER_INFLUXDB_INIT_ORG=zephyr \
		-e DOCKER_INFLUXDB_INIT_BUCKET=weather \
		influxdb:2.7-alpine
	@echo "$(GREEN)✅ InfluxDB started on port 8086$(NC)"

.PHONY: db-start
db-start: redis-start influx-start ## Start all databases
	@echo "$(GREEN)✅ All databases started$(NC)"

.PHONY: db-stop
db-stop: ## Stop all databases
	@echo "$(YELLOW)⏹️  Stopping databases...$(NC)"
	@docker stop zephyr-redis zephyr-influx 2>/dev/null || true
	@docker rm zephyr-redis zephyr-influx 2>/dev/null || true
	@echo "$(GREEN)✅ Databases stopped$(NC)"

# ==============================================================================
# Utilities
# ==============================================================================

.PHONY: clean
clean: proto-clean ## Clean build artifacts
	@echo "$(YELLOW)🧹 Cleaning build artifacts...$(NC)"
	@rm -rf $(BUILD_DIR)
	@rm -f coverage.out coverage.html
	@go clean -cache
	@echo "$(GREEN)✅ Clean complete$(NC)"

.PHONY: deps-update
deps-update: ## Update all Go dependencies
	@echo "$(BLUE)⬆️  Updating dependencies...$(NC)"
	@go get -u ./...
	@go mod tidy
	@echo "$(GREEN)✅ Dependencies updated$(NC)"

.PHONY: deps-check
deps-check: ## Check for dependency vulnerabilities
	@echo "$(BLUE)🔒 Checking for vulnerabilities...$(NC)"
	@govulncheck ./...
	@echo "$(GREEN)✅ Vulnerability check complete$(NC)"

.PHONY: mod-graph
mod-graph: ## Show module dependency graph
	@echo "$(BLUE)📊 Generating module dependency graph...$(NC)"
	@go mod graph

.PHONY: mod-why
mod-why: ## Explain why dependencies are needed (requires MODULE param)
	@echo "$(BLUE)❓ Explaining dependency: $(MODULE)$(NC)"
	@go mod why $(MODULE)

.PHONY: size
size: build ## Show binary sizes
	@echo "$(BLUE)📏 Binary sizes:$(NC)"
	@ls -lh $(BUILD_DIR)/

.PHONY: info
info: ## Show project information
	@echo "$(CYAN)Project Information:$(NC)"
	@echo "  Version: $(VERSION)"
	@echo "  Commit: $(COMMIT)"
	@echo "  Build Time: $(BUILD_TIME)"
	@echo "  Go Version: $(shell go version)"
	@echo "  Buf Version: $(shell buf --version 2>/dev/null || echo 'not installed')"
	@echo "  Golangci-lint: $(shell golangci-lint --version 2>/dev/null | head -1 || echo 'not installed')"

.PHONY: env
env: ## Show environment variables
	@echo "$(CYAN)Environment Variables:$(NC)"
	@echo "  GOPATH: $(GOPATH)"
	@echo "  GOROOT: $(GOROOT)"
	@echo "  GOOS: $(GOOS)"
	@echo "  GOARCH: $(GOARCH)"
	@echo "  GO111MODULE: $(GO111MODULE)"

.PHONY: check-deps
check-deps: ## Check if required tools are installed
	@echo "$(BLUE)🔍 Checking required tools...$(NC)"
	@command -v go >/dev/null 2>&1 || (echo "$(RED)❌ Go is not installed$(NC)" && exit 1)
	@command -v buf >/dev/null 2>&1 || (echo "$(YELLOW)⚠️  buf is not installed. Run 'make install-tools'$(NC)")
	@command -v golangci-lint >/dev/null 2>&1 || (echo "$(YELLOW)⚠️  golangci-lint is not installed. Run 'make install-tools'$(NC)")
	@command -v docker >/dev/null 2>&1 || (echo "$(YELLOW)⚠️  Docker is not installed$(NC)")
	@echo "$(GREEN)✅ Tool check complete$(NC)"

.PHONY: workspace-status
workspace-status: ## Show workspace status
	@echo "$(CYAN)Workspace Status:$(NC)"
	@echo "  Git status:"
	@git status --porcelain || echo "    Not a git repository"
	@echo "  Go mod status:"
	@go mod verify
	@echo "  Generated files:"
	@find $(GEN_DIR) -name "*.go" -type f | wc -l | xargs echo "    Proto files:"

# ==============================================================================
# CI/CD
# ==============================================================================

.PHONY: ci
ci: deps generate check security test ## Run full CI pipeline
	@echo "$(GREEN)✅ CI pipeline completed successfully$(NC)"

.PHONY: ci-quick
ci-quick: deps generate format vet test-unit ## Run quick CI checks
	@echo "$(GREEN)✅ Quick CI pipeline completed$(NC)"

.PHONY: pre-commit
pre-commit: format proto-format generate check ## Run pre-commit checks
	@echo "$(GREEN)✅ Pre-commit checks passed$(NC)"

.PHONY: pre-push
pre-push: ci test-integration ## Run pre-push checks
	@echo "$(GREEN)✅ Pre-push checks passed$(NC)"

.PHONY: release-check
release-check: clean ci cross-compile ## Run full release checks
	@echo "$(GREEN)✅ Release checks completed$(NC)"

.PHONY: tag
tag: ## Create a new git tag (requires VERSION=x.x.x)
	@if [ -z "$(VERSION)" ]; then \
		echo "$(RED)❌ VERSION is required. Usage: make tag VERSION=1.0.0$(NC)"; \
		exit 1; \
	fi
	@echo "$(BLUE)🏷️  Creating tag $(VERSION)...$(NC)"
	@git tag -a v$(VERSION) -m "Release v$(VERSION)"
	@git push origin v$(VERSION)
	@echo "$(GREEN)✅ Tag v$(VERSION) created and pushed$(NC)"

# ==============================================================================
# Documentation
# ==============================================================================

.PHONY: docs
docs: ## Generate documentation
	@echo "$(BLUE)📚 Generating documentation...$(NC)"
	@mkdir -p docs
	@go doc -all ./... > docs/API.md
	@echo "$(GREEN)✅ Documentation generated in docs/API.md$(NC)"

.PHONY: docs-serve
docs-serve: ## Serve documentation locally
	@echo "$(BLUE)📚 Serving documentation on :6060$(NC)"
	@echo "$(CYAN)Open http://localhost:6060/pkg/ in your browser$(NC)"
	@godoc -http=:6060

.PHONY: docs-proto
docs-proto: ## Generate Protocol Buffer documentation
	@echo "$(BLUE)📚 Generating Protocol Buffer documentation...$(NC)"
	@buf generate --template buf.gen.docs.yaml || echo "$(YELLOW)⚠️  Docs template not found, skipping$(NC)"

# ==============================================================================
# Git Hooks
# ==============================================================================

.PHONY: install-hooks
install-hooks: ## Install git hooks
	@echo "$(BLUE)🪝 Installing git hooks...$(NC)"
	@echo '#!/bin/sh\nmake pre-commit' > .git/hooks/pre-commit
	@echo '#!/bin/sh\nmake pre-push' > .git/hooks/pre-push
	@chmod +x .git/hooks/pre-commit .git/hooks/pre-push
	@echo "$(GREEN)✅ Git hooks installed$(NC)"

.PHONY: uninstall-hooks
uninstall-hooks: ## Remove git hooks
	@echo "$(BLUE)🪝 Removing git hooks...$(NC)"
	@rm -f .git/hooks/pre-commit .git/hooks/pre-push
	@echo "$(GREEN)✅ Git hooks removed$(NC)"

# ==============================================================================
# Monitoring & Profiling
# ==============================================================================

.PHONY: profile-cpu
profile-cpu: ## Run CPU profiling
	@echo "$(BLUE)📊 Running CPU profiling...$(NC)"
	@go test -cpuprofile=cpu.prof -bench=. ./...
	@go tool pprof cpu.prof

.PHONY: profile-mem
profile-mem: ## Run memory profiling
	@echo "$(BLUE)📊 Running memory profiling...$(NC)"
	@go test -memprofile=mem.prof -bench=. ./...
	@go tool pprof mem.prof

.PHONY: trace
trace: ## Run execution trace
	@echo "$(BLUE)🔍 Running execution trace...$(NC)"
	@go test -trace=trace.out -bench=. ./...
	@go tool trace trace.out

# Default target
.DEFAULT_GOAL := help