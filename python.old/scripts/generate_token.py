#!/usr/bin/env python3

# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import base64
import secrets


def generate_secure_token(length: int = 32) -> str:
    """Generate a secure token with given length of bytes (default 32 bytes = 256 bits)."""
    # Generate random bytes
    token_bytes = secrets.token_bytes(length)
    # Convert to URL-safe base64 and remove padding
    token = base64.urlsafe_b64encode(token_bytes).decode("utf-8").rstrip("=")
    return token


if __name__ == "__main__":
    token = generate_secure_token()
    print("\nGenerated secure API token:")
    print("---------------------------")
    print(token)
    print("\nMake sure to update your .env file with:")
    print("API_TOKEN=" + token)
    print("---------------------------\n")
