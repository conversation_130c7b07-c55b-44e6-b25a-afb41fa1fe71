# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import asyncio
import os
from datetime import UTC, datetime, timedelta
from enum import Enum
from typing import Literal, TypedDict

from dotenv import load_dotenv

from app.services.weather_providers.visual_crossing import VisualCrossingClient

# Predefined locations with their coordinates (latitude, longitude)
AVAILABLE_LOCATIONS = {
    "London": (51.5074, -0.1278),
    "Paris": (48.8566, 2.3522),
    "Berlin": (52.5200, 13.4050),
    "Rome": (41.9028, 12.4964),
    "Madrid": (40.4168, -3.7038),
    "Amsterdam": (52.3676, 4.9041),
    "Vienna": (48.2082, 16.3738),
    "Stockholm": (59.3293, 18.0686),
    "Prague": (50.0755, 14.4378),
    "Barcelona": (41.3851, 2.1734),
    "Sion": (46.2044, 7.3471),
    "Grand-Dixence": (46.0821, 7.3967),
}


class RequestType(Enum):
    """Type of weather data request."""

    FORECAST = "forecast"
    HISTORICAL = "historical"


class WeatherConfig(TypedDict):
    """Configuration for weather data fetching."""

    location: str  # key from AVAILABLE_LOCATIONS
    request_type: RequestType  # forecast or historical
    historical_days: int | None  # number of days to look back (for historical)
    indicators: list[str] | None  # specific indicators to fetch
    groups: list[str] | None  # groups of indicators to fetch
    resolutions: list[Literal["minute", "hourly", "daily"]]  # time resolutions to display
    display_points: int  # number of points to display at start/end


# Configuration
CONFIG: WeatherConfig = {
    # Location selection
    "location": "Sion",  # change this to any key from AVAILABLE_LOCATIONS
    # Request type configuration
    "request_type": RequestType.FORECAST,  # FORECAST or HISTORICAL
    "historical_days": 7,  # Only used if request_type is HISTORICAL (max 365 days)
    # Data selection
    # Option 1: specific indicators (set to None if using groups)
    "indicators": [
        # "temperature",
        # "precipitation",
        # "wind_speed",
    ],
    # Option 2: groups of related indicators (set indicators to None if using groups)
    "groups": [
        # "temperature",
        # "precipitation",
        # Uncomment groups you want to include:
        "wind",
        "solar",
        # "solar_radiation",
        # "atmosphere",
        # "wind_height",
        # "general",
        # "consumption",
        # "solar_prod",
        # "wind_onshore",
        # "wind_offshore",
    ],
    # Display configuration
    "resolutions": ["hourly", "daily"],  # which time resolutions to display
    "display_points": 5,  # number of points to show at start/end
}


async def main() -> None:
    """Fetch and display weather data for a single location."""
    # Load API key from environment
    load_dotenv(override=True)
    api_key = os.getenv("VISUAL_CROSSING_API_KEY")
    if not api_key:
        raise ValueError("VISUAL_CROSSING_API_KEY not found in environment")

    # Validate location
    if CONFIG["location"] not in AVAILABLE_LOCATIONS:
        raise ValueError(
            f"Location '{CONFIG['location']}' not found in AVAILABLE_LOCATIONS. "
            f"Available locations: {', '.join(sorted(AVAILABLE_LOCATIONS.keys()))}"
        )

    # Initialize client
    client = VisualCrossingClient([api_key])

    try:
        print(f"\n=== Weather {CONFIG['request_type'].value.title()} for {CONFIG['location']} ===")
        lat, lon = AVAILABLE_LOCATIONS[CONFIG["location"]]

        # Set up time range for historical requests
        start = end = None
        if CONFIG["request_type"] == RequestType.HISTORICAL:
            if not CONFIG["historical_days"]:
                raise ValueError("historical_days must be set for historical requests")
            end = datetime.now(UTC)
            start = end - timedelta(days=CONFIG["historical_days"])
            print(
                f"Fetching data from {start.strftime('%Y-%m-%d %H:%M:%S')} "
                f"to {end.strftime('%Y-%m-%d %H:%M:%S')}"
            )

            # Fetch historical data
            results = await client.fetch_historical(
                lat=lat,
                lon=lon,
                indicators=CONFIG["indicators"],
                groups=CONFIG["groups"],
                start=start,
                end=end,
            )
        else:
            # Fetch forecast data
            results = await client.fetch_forecast(
                lat=lat,
                lon=lon,
                indicators=CONFIG["indicators"],
                groups=CONFIG["groups"],
            )

        # Display results for each time resolution
        for resolution in CONFIG["resolutions"]:
            if not results[resolution]:
                print(f"\nNo data for {resolution}")
                continue

            print(f"\n{resolution.capitalize()} Resolution Data:")
            for indicator, data in sorted(results[resolution].items()):
                if not data["values"] or not data["times"]:
                    print(f"\nNo data for {indicator}")
                    continue

                chunk_size = CONFIG["display_points"]
                if len(data["values"]) <= chunk_size * 2:
                    chunk_size = len(data["values"])

                print(f"\n  {indicator}:")
                # Print first N points
                for value, time in zip(
                    data["values"][:chunk_size],
                    data["times"][:chunk_size],
                    strict=True,
                ):
                    if isinstance(time, datetime):
                        time = time.strftime("%Y-%m-%d %H:%M:%S")
                    print(f"    {time}: {value}")

                if len(data["values"]) > chunk_size * 2:
                    print("    ...")
                    # Print last N points
                    for value, time in zip(
                        data["values"][-chunk_size:],
                        data["times"][-chunk_size:],
                        strict=True,
                    ):
                        if isinstance(time, datetime):
                            time = time.strftime("%Y-%m-%d %H:%M:%S")
                        print(f"    {time}: {value}")

    finally:
        # Clean up client resources
        await client._cleanup_session()


if __name__ == "__main__":
    asyncio.run(main())
