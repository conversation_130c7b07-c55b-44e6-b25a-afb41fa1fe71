# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import asyncio
import logging

from app.services.influx import influx_service
from app.services.redis import redis_service

logger = logging.getLogger(__name__)


async def cleanup_inactive_locations() -> None:
    """
    Cleanup script to remove data for inactive locations from InfluxDB.
    This script:
    1. Gets all active locations from Redis
    2. Deletes data older than INACTIVE_CLEANUP_DAYS for locations not in Redis
    """
    try:
        logger.info("Starting InfluxDB cleanup process")

        # get active locations from Redis
        active_locations = await redis_service.get_active_locations()
        logger.info(f"Found {len(active_locations)} active locations")

        # cleanup inactive locations
        await influx_service.cleanup_inactive_locations()
        logger.info("Successfully cleaned up inactive location data")

    except Exception as e:
        logger.error(f"Error during cleanup: {e!s}")
        raise


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )
    asyncio.run(cleanup_inactive_locations())
