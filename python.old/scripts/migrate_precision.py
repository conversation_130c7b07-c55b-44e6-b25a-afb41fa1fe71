# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import asyncio
import logging

from app.core.registry import LocationRegistry
from app.models.location import LocationCreate
from app.services.redis import redis_service

logger = logging.getLogger(__name__)


async def migrate_location_precision() -> None:
    """
    Migration script to update location precision.
    This script:
    1. Gets all active locations
    2. Re-normalizes coordinates with new precision
    3. Updates location IDs and data in Redis
    4. Updates location tags in InfluxDB
    """
    try:
        logger.info("Starting location precision migration")

        # get all active locations
        active_locations = await redis_service.get_active_locations()
        logger.info(f"Found {len(active_locations)} active locations to migrate")

        registry = LocationRegistry()
        migrations: list[dict] = []

        # process each location
        for old_location_id in active_locations:
            try:
                # get current location data
                location_data = await redis_service.get_location(old_location_id)
                if not location_data:
                    continue

                # create new location with current precision
                location_create = LocationCreate(
                    latitude=location_data["latitude"],
                    longitude=location_data["longitude"],
                    types=location_data["types"],
                )

                new_location = await registry.register_location(location_create)
                new_location_id = new_location.id

                if new_location_id != old_location_id:
                    migrations.append(
                        {
                            "old_id": old_location_id,
                            "new_id": new_location_id,
                            "location": new_location,
                        }
                    )

            except Exception as e:
                logger.error(f"Error processing location {old_location_id}: {e!s}")

        # perform migrations
        for migration in migrations:
            old_id = migration["old_id"]
            new_id = migration["new_id"]
            location = migration["location"]

            try:
                logger.info(f"Migrating location {old_id} to {new_id}")

                # update in Redis
                await redis_service.deactivate_location(old_id)
                await redis_service.add_active_location(new_id, location.dict())

                # Note: InfluxDB data migration would require more complex logic
                # to update tags and potentially merge data points
                logger.warning(f"InfluxDB data for {old_id} needs manual migration")

            except Exception as e:
                logger.error(f"Error migrating location {old_id}: {e!s}")

        logger.info(f"Successfully migrated {len(migrations)} locations")

    except Exception as e:
        logger.error(f"Error during migration: {e!s}")
        raise


if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )
    asyncio.run(migrate_location_precision())
