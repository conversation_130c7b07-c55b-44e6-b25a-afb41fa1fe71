# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import asyncio
import os

from dotenv import load_dotenv

from app.services.weather_providers.visual_crossing import VisualCrossingClient


async def main() -> None:
    """Example script demonstrating the use of VisualCrossingClient."""
    # Load API key from environment
    load_dotenv(override=True)
    api_key = os.getenv("VISUAL_CROSSING_API_KEY")
    if not api_key:
        raise ValueError("VISUAL_CROSSING_API_KEY not found in environment")

    # Initialize client
    client = VisualCrossingClient([api_key])

    # Example coordinates for European cities
    locations = {
        "London": (51.5074, -0.1278),
        "Paris": (48.8566, 2.3522),
        "Berlin": (52.5200, 13.4050),
        "Rome": (41.9028, 12.4964),
        "Madrid": (40.4168, -3.7038),
    }

    try:
        # Example 1: Fetch 15-day temperature forecast for London
        print("\n=== 15-Day Temperature Forecast for London ===")
        london_lat, london_lon = locations["London"]
        results = await client.fetch_forecast(
            lat=london_lat,
            lon=london_lon,
            indicators=["temperature"],
            # groups=["temperature"],  # Gets all temperature-related indicators
        )

        # Print the first 5 forecast points for hourly temperature
        for indicator, data in results["hourly"].items():
            if data["values"] and data["times"]:
                print(f"\n{indicator} (hourly):")
                # Print first 5 points
                for value, time in zip(data["values"][:5], data["times"][:5], strict=True):
                    print(f"  {time}: {value}°C")
                print("  ...")
                # Print last 5 points
                for value, time in zip(data["values"][-5:], data["times"][-5:], strict=True):
                    print(f"  {time}: {value}°C")

        # Example 2: Fetch precipitation forecast for Paris
        print("\n=== Precipitation Forecast for Paris ===")
        paris_lat, paris_lon = locations["Paris"]
        results = await client.fetch_forecast(
            lat=paris_lat, lon=paris_lon, groups=["precipitation"]
        )

        # Print next 24 hours of precipitation data for each resolution
        for resolution in ["hourly", "daily"]:
            for indicator, data in results[resolution].items():
                if data["values"] and data["times"]:
                    print(f"\n{indicator} ({resolution}):")
                    # Print first 5 points
                    for value, time in zip(data["values"][:5], data["times"][:5], strict=True):
                        print(f"  {time}: {value}")
                    if len(data["values"]) > 5:
                        print("  ...")
                        # Print last 5 points
                        for value, time in zip(
                            data["values"][-5:], data["times"][-5:], strict=True
                        ):
                            print(f"  {time}: {value}")

        # Example 3: Comprehensive weather forecast for Berlin
        print("\n=== Comprehensive Weather Forecast for Berlin ===")
        berlin_lat, berlin_lon = locations["Berlin"]
        forecast_groups = ["temperature", "wind", "precipitation", "atmosphere"]
        results = await client.fetch_forecast(
            lat=berlin_lat, lon=berlin_lon, groups=forecast_groups
        )

        # Print next 12 hours of all hourly indicators
        print("\nNext 12 hours forecast:")
        if results["hourly"]:
            # Get first 12 timestamps from any hourly indicator
            first_indicator_data = next(iter(results["hourly"].values()))
            times = first_indicator_data["times"][:12] if first_indicator_data["times"] else []

            for time in times:
                print(f"\nAt {time}:")
                for indicator, data in results["hourly"].items():
                    if time in data["times"]:
                        time_index = data["times"].index(time)
                        value = data["values"][time_index]
                        print(f"  {indicator}: {value}")

        # Example 4: Solar forecast for Rome (useful for solar power prediction)
        print("\n=== Solar Energy Forecast for Rome ===")
        rome_lat, rome_lon = locations["Rome"]
        results = await client.fetch_forecast(
            lat=rome_lat, lon=rome_lon, groups=["solar", "solar_radiation"]
        )

        # Print daylight hours forecast for both hourly and daily data
        print("\nDaylight hours forecast:")
        for resolution in ["hourly", "daily"]:
            for indicator, data in results[resolution].items():
                if data["values"] and data["times"]:
                    print(f"\n{indicator} ({resolution}):")
                    # Only print non-zero values (daylight hours)
                    for value, time in zip(data["values"][:24], data["times"][:24], strict=True):
                        if not isinstance(value, int | float) or value > 0:
                            print(f"  {time}: {value}")

        # Example 5: Wind forecast at different heights for Madrid
        print("\n=== Wind Forecast at Different Heights for Madrid ===")
        madrid_lat, madrid_lon = locations["Madrid"]
        results = await client.fetch_forecast(
            lat=madrid_lat, lon=madrid_lon, groups=["wind", "wind_height"]
        )

        # Print next 6 hours of wind data at all heights
        print("\nNext 6 hours of wind data:")
        if results["hourly"]:
            # Get first 6 timestamps from any hourly indicator
            first_indicator_data = next(iter(results["hourly"].values()))
            times = first_indicator_data["times"][:6] if first_indicator_data["times"] else []
            wind_indicators = sorted(results["hourly"].keys())  # Sort for consistent output

            for time in times:
                print(f"\nAt {time}:")
                for indicator in wind_indicators:
                    data = results["hourly"][indicator]
                    if time in data["times"]:
                        time_index = data["times"].index(time)
                        value = data["values"][time_index]
                        print(f"  {indicator}: {value}")

    finally:
        # Clean up client resources
        await client._cleanup_session()


if __name__ == "__main__":
    asyncio.run(main())
