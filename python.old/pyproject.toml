[build-system]
requires = [
    "setuptools>=42",
    "wheel"
]
build-backend = "setuptools.build_meta"

[project]
name = "zephyr"
version = "0.1.0"
description = "A scalable, event-driven, near-real-time weather service"
readme = "README.md"
authors = [
    { name = "Emissium", email = "<EMAIL>" }
]
license = { file = "LICENSE" }
keywords = [
    "weather",
    "api",
    "emissium",
    "real-time",
    "forecast"
]
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: Emissium :: Private License",
    "Operating System :: OS Independent"
]
requires-python = ">=3.12, <4"

dependencies = [
    "fastapi==0.109.0",
    "uvicorn==0.27.0",
    "pandas==2.2.3",
    "pydantic==2.6.0",
    "pydantic-settings==2.8.1",
    "python-dotenv==1.0.1",
    "redis==5.0.1",
    "influxdb-client==1.40.0",
    "aiohttp==3.9.3",
    "python-jose==3.3.0",
    "passlib==1.7.4",
    "bcrypt==4.1.2"
]

[project.optional-dependencies]
dev = [
    "pytest>=8.0.0, <9",
    "pytest-asyncio>=0.23.0, <1",
    "pytest-cov>=4.1.0, <5",
    "httpx>=0.26.0, <1"
]

[project.urls]
Homepage = "https://github.com/emissium/zephyr"
Documentation = "https://github.com/emissium/zephyr#readme"
Issues = "https://github.com/emissium/zephyr/issues"

[tool.setuptools.packages.find]
where = ["."]

[tool.ruff]
lint.select = [
    "E",    # Enforces PEP8 error rules (indentation, spacing, maximum line legnth)
    "W",    # Enforces style warnings (line breaks, whitespace issues)
    "F",    # Detects undefined names, unused imports, and syntax errors
    "B",    # Identifies common bugs and potential design issues
    "I",    # Sorts and organizes imports consistently
    "UP",   # Enforces modern Python syntax (compatible with current Python versions)
    "DTZ",  # Warns against naive datetime objects without timezone
    "C4",   # Encourages use of comprehensions for performance and readability
    "S",    # Detects common security issues (e.g., insecure subprocess, eval usage)
    "PERF", # Flags inefficient code patterns affecting performance
    "PT",   # Enforces best practices in test code (e.g., unnecessary asserts)
    "N",    # Ensures consistent naming conventions for functions, classes, variables
    "ANN",  # Requires functions to have type hints (parameters and return types)
    "SIM",  # Flags overly complex code that can be simplified
    "RUF"   # Additional rules optimized by Ruff for speed and accuracy
]
lint.ignore = ["RUF001", "N806", "W293"]
line-length = 100

[tool.ruff.format]
line-ending = "lf"
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false

[tool.ruff.lint.per-file-ignores]
"tests/*" = ["S101"]