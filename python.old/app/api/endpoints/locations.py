# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import logging

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

from app.core.registry import LocationRegistry
from app.core.variables import GROUP_TO_VARIABLES, VARIABLE_METADATA, WeatherGroup
from app.models.location import Location, LocationCreate
from app.services.redis import redis_service

logger = logging.getLogger(__name__)
router = APIRouter()
location_registry = LocationRegistry()


class LocationResponse(BaseModel):
    location_id: str = Field(description="Normalized location identifier")
    lat: float = Field(description="Normalized latitude")
    lon: float = Field(description="Normalized longitude")
    types: list[str] = Field(description="Active location types")
    last_poll: str | None = Field(description="Timestamp of last successful poll", default=None)


class WeatherVariableInfo(BaseModel):
    """Information about a weather variable."""

    name: str = Field(description="Name of the weather variable")
    description: str = Field(description="Description of what this variable measures")
    unit: str | None = Field(description="Unit of measurement")


class LocationTypeResponse(BaseModel):
    """Response model for location types and their weather variables."""

    type_name: str = Field(description="Name of the location type (weather group)")
    variables: list[WeatherVariableInfo] = Field(
        description="Weather variables available for this type"
    )


@router.post(
    "/locations",
    response_model=LocationResponse,
    status_code=201,
    summary="Add or activate a location",
    description=(
        "Register a new location or activate an existing one for weather data collection. "
        "Coordinates will be normalized to avoid duplication. Triggers immediate data fetch "
        "and schedules regular polling for the specified weather variable types."
    ),
)
async def create_location(location: LocationCreate) -> LocationResponse:
    try:
        # normalize and register location
        registered_location = await location_registry.register_location(location)

        # store in Redis with TTL
        await redis_service.add_active_location(
            registered_location.id, registered_location.model_dump()
        )

        return LocationResponse(
            location_id=registered_location.id,
            lat=registered_location.normalized_lat,
            lon=registered_location.normalized_lon,
            types=registered_location.types,
            last_poll=(
                registered_location.last_polled.isoformat()
                if registered_location.last_polled
                else None
            ),
        )
    except Exception as e:
        logger.error(f"Error creating location: {e!s}")
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get(
    "/locations/{location_id}",
    response_model=LocationResponse,
    summary="Get location details",
    description="Retrieve metadata and normalized information for a specific location.",
)
async def get_location(location_id: str) -> LocationResponse:
    location_data = await redis_service.get_location(location_id)
    if not location_data:
        raise HTTPException(status_code=404, detail="Location not found")

    location = Location(**location_data)
    return LocationResponse(
        location_id=location_id,
        lat=location.normalized_lat,
        lon=location.normalized_lon,
        types=location.types,
        last_poll=(location.last_polled.isoformat() if location.last_polled else None),
    )


@router.get(
    "/locations",
    response_model=list[LocationResponse],
    summary="List active locations",
    description="Return a list of all currently active locations and their metadata.",
)
async def list_locations() -> list[LocationResponse]:
    try:
        active_locations = await redis_service.get_active_locations()
        locations = []
        for location_id in active_locations:
            location_data = await redis_service.get_location(location_id)
            if location_data:
                location = Location(**location_data)
                locations.append(
                    LocationResponse(
                        location_id=location.id,
                        lat=location.normalized_lat,
                        lon=location.normalized_lon,
                        types=location.types,
                        last_poll=(
                            location.last_polled.isoformat() if location.last_polled else None
                        ),
                    )
                )
        return locations
    except Exception as e:
        logger.error(f"Error listing locations: {e!s}")
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.delete(
    "/locations/{location_id}",
    status_code=200,
    summary="Deactivate a location",
    description=(
        "Manually deactivate a location to stop weather data collection. "
        "Note: Locations are automatically deactivated after TTL expiration."
    ),
)
async def delete_location(location_id: str) -> dict[str, str]:
    try:
        # check if location exists
        location_data = await redis_service.get_location(location_id)
        if not location_data:
            raise HTTPException(status_code=404, detail="Location not found")

        # deactivate location in Redis
        await redis_service.deactivate_location(location_id)
        return {"message": "Location deactivated successfully"}
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"Error deleting location: {e!s}")
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get(
    "/locations/types",
    response_model=list[LocationTypeResponse],
    summary="Get available location types",
    description=(
        "Return a list of all available location types (weather groups) and their corresponding "
        "weather variables. Each type includes a description and the complete list of "
        "variables that can be measured for that type."
    ),
)
async def get_location_types() -> list[LocationTypeResponse]:
    """
    This endpoint returns all available location types and their corresponding weather variables.
    The types are based on the WeatherGroup enum and the variables are mapped using GROUP_TO_VARIABLES.
    """

    location_types = []
    for group in WeatherGroup:
        variables = GROUP_TO_VARIABLES[group.value]
        variable_info = []
        for var_name in variables:
            metadata = VARIABLE_METADATA[var_name]
            # ensure we get string values for description and unit
            description = str(metadata["description"]) if metadata.get("description") else ""
            unit = str(metadata["unit"]) if metadata.get("unit") not in (None, "None") else None

            variable_info.append(
                WeatherVariableInfo(name=var_name, description=description, unit=unit)
            )

        location_types.append(
            LocationTypeResponse(
                type_name=group.value,
                variables=sorted(variable_info, key=lambda x: x.name),
            )
        )

    return sorted(location_types, key=lambda x: x.type_name)
