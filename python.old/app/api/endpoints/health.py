# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import logging
import sys

from fastapi import APIRouter, HTTPException

from app.config import settings
from app.models.health import HealthStatus, SystemStatus, VersionInfo
from app.services.influx import influx_service
from app.services.redis import redis_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get(
    "/health",
    response_model=HealthStatus,
    summary="Service health check",
    description="Simple healthcheck endpoint that verifies connectivity to Redis and InfluxDB.",
)
async def health_check() -> HealthStatus:
    try:
        # check Redis connection
        redis_ok = await _check_redis()
        # check InfluxDB connection
        influx_ok = await _check_influxdb()

        return HealthStatus(
            status="healthy" if redis_ok and influx_ok else "degraded",
            redis=redis_ok,
            influxdb=influx_ok,
        )
    except Exception as e:
        logger.error(f"Health check failed: {e!s}")
        raise HTTPException(status_code=500, detail="Health check failed") from e


async def _check_redis() -> bool:
    try:
        # try to ping Redis
        return await redis_service.redis.ping() == "PONG"
    except Exception as e:
        logger.error(f"Redis health check failed: {e!s}")
        return False


async def _check_influxdb() -> bool:
    try:
        # try to query InfluxDB health
        health = influx_service.client.health()
        return health.status == "pass"
    except Exception as e:
        logger.error(f"InfluxDB health check failed: {e!s}")
        return False


@router.get(
    "/status",
    response_model=SystemStatus,
    summary="System status",
    description=(
        "Return detailed system status including number of active locations, "
        "polling interval, and worker status."
    ),
)
async def system_status() -> SystemStatus:
    try:
        # get active locations count from Redis
        active_locations = len(await redis_service.redis.keys("location:*"))

        # get last poll time
        last_poll_str = await redis_service.redis.get("last_poll_time")
        last_poll_time = last_poll_str.decode() if last_poll_str else None

        # get worker status
        worker_status = await redis_service.redis.get("worker_status")
        worker_status = worker_status.decode() if worker_status else "unknown"

        return SystemStatus(
            active_locations=active_locations,
            polling_interval_minutes=settings.POLLING_INTERVAL // 60,
            last_poll_time=last_poll_time,
            worker_status=worker_status,
        )
    except Exception as e:
        logger.error(f"Error getting system status: {e!s}")
        raise HTTPException(status_code=500, detail="Failed to get system status") from e


@router.get(
    "/version",
    response_model=VersionInfo,
    summary="API version info",
    description="Return API version information including build date and git commit.",
)
async def version_info() -> VersionInfo:
    try:
        return VersionInfo(
            version=settings.VERSION,
            build_date=settings.BUILD_DATE,
            git_commit=settings.GIT_COMMIT,
            python_version=f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        )
    except Exception as e:
        logger.error(f"Error getting version info: {e!s}")
        raise HTTPException(status_code=500, detail="Failed to get version info") from e
