# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import logging
from collections.abc import Callable, Coroutine
from datetime import UTC, datetime, timedelta
from functools import wraps
from typing import Annotated, Any, TypeVar

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field

from app.core.variables import VARIABLE_METADATA, WeatherGroup
from app.models.weather import WeatherData
from app.services.influx import influx_service
from app.services.redis import redis_service

logger = logging.getLogger(__name__)
router = APIRouter()

T = TypeVar("T")


class WeatherResponse(BaseModel):
    """Response model for weather endpoints."""

    location_id: str = Field(..., description="Location identifier")
    data: WeatherData = Field(..., description="Weather measurements")


class WeatherVariableMetadata(BaseModel):
    """Metadata for a weather variable."""

    name: str = Field(description="Name of the weather variable")
    description: str = Field(description="Description of what this variable measures")
    unit: str | None = Field(description="Unit of measurement")
    groups: list[str] = Field(description="Weather groups this variable belongs to")
    type: str = Field(description="Data type of the variable")
    comment: str | None = Field(description="Additional usage notes", default=None)


async def validate_location(location_id: str) -> dict:
    """Validate location exists and is active."""
    location_data = await redis_service.get_location(location_id)
    if not location_data:
        raise HTTPException(status_code=404, detail="Location not found or inactive")
    return location_data


def create_weather_response(
    location_id: str, location_types: list, weather_data: dict
) -> WeatherResponse:
    """Create a standardized weather response."""
    return WeatherResponse(
        location_id=location_id,
        data=WeatherData(
            location_id=location_id,
            location_types=location_types,
            data=weather_data,
        ),
    )


def handle_weather_errors(
    func: Callable[..., Coroutine[Any, Any, T]],
) -> Callable[..., Coroutine[Any, Any, T]]:
    """Decorator to handle common weather endpoint errors."""

    @wraps(func)
    async def wrapper(location_id: str, *args: str, **kwargs: Any) -> T:  # noqa: ANN401
        try:
            return await func(location_id, *args, **kwargs)
        except HTTPException as he:
            raise he
        except Exception as e:
            logger.error(f"Error in {func.__name__}: {e!s}")
            raise HTTPException(status_code=500, detail=str(e)) from e

    return wrapper


@router.get(
    "/weather/{location_id}",
    response_model=WeatherResponse,
    summary="Get current weather",
    description=(
        "Return the most recent weather data for a location. "
        "Data is returned from cache if available, otherwise fetched from InfluxDB."
    ),
)
@handle_weather_errors
async def get_current_weather(location_id: str) -> WeatherResponse:
    location_data = await validate_location(location_id)

    # try to get from cache first
    weather_data = await redis_service.get_cached_recent(location_id)
    if weather_data:
        return create_weather_response(location_id, location_data["types"], weather_data)

    # if not in cache, get from InfluxDB
    weather_data = await influx_service.get_recent_weather(location_id)
    if weather_data:
        return create_weather_response(location_id, location_data["types"], weather_data)

    raise HTTPException(status_code=404, detail="No weather data available")


@router.get(
    "/weather/{location_id}/history",
    response_model=WeatherResponse,
    summary="Get historical weather data",
    description=(
        "Return historical weather data for a location within a specified time window. "
        "Data is retrieved from InfluxDB."
    ),
)
@handle_weather_errors
async def get_historical_weather(
    location_id: str,
    from_time: Annotated[
        datetime,
        Query(
            description="Start time (format: YYYY-MM-DDTHH:MM:SS)",
            example="2024-03-27T00:00:00",
        ),
    ],
    to_time: Annotated[
        datetime,
        Query(
            description="End time (format: YYYY-MM-DDTHH:MM:SS)",
            example="2024-03-27T23:59:59",
        ),
    ],
) -> WeatherResponse:
    # validate time range
    if to_time <= from_time:
        raise HTTPException(status_code=400, detail="End time must be after start time")

    if to_time - from_time > timedelta(days=30):
        raise HTTPException(status_code=400, detail="Time range cannot exceed 30 days")

    location_data = await validate_location(location_id)

    # get historical data from InfluxDB
    weather_data = await influx_service.get_historical_weather(location_id, from_time, to_time)
    if not weather_data:
        raise HTTPException(
            status_code=404,
            detail="No weather data available for this time range",
        )

    return create_weather_response(location_id, location_data["types"], weather_data)


@router.get(
    "/weather/{location_id}/forecast",
    response_model=WeatherResponse,
    summary="Get weather forecast",
    description=(
        "Return the latest 14-day weather forecast for a location. "
        "Data is returned from cache if available, otherwise fetched from InfluxDB."
    ),
)
@handle_weather_errors
async def get_weather_forecast(location_id: str) -> WeatherResponse:
    location_data = await validate_location(location_id)

    # try to get from cache first
    forecast_data = await redis_service.get_cached_forecast(location_id)
    if forecast_data:
        return create_weather_response(location_id, location_data["types"], forecast_data)

    # if not in cache, get from InfluxDB
    start_time = datetime.now(UTC)
    end_time = start_time + timedelta(days=14)

    forecast_data = await influx_service.get_historical_weather(location_id, start_time, end_time)
    if not forecast_data:
        raise HTTPException(status_code=404, detail="No forecast data available")

    return create_weather_response(location_id, location_data["types"], forecast_data)


@router.get(
    "/weather/variables",
    response_model=list[WeatherVariableMetadata],
    summary="Get available weather variables",
    description=(
        "Return a list of all available weather variables with their complete metadata, "
        "including units, descriptions, applicable weather groups, and data types."
    ),
)
async def get_weather_variables() -> list[WeatherVariableMetadata]:
    variables = []
    for var_name, metadata in VARIABLE_METADATA.items():
        # Convert groups set to list of group values
        groups = []
        metadata_groups = metadata.get("groups")
        if isinstance(metadata_groups, set) and all(
            isinstance(g, WeatherGroup) for g in metadata_groups
        ):
            groups = sorted(g.value for g in metadata_groups)

        # Get the variable type as string
        var_type = "string"
        metadata_type = metadata.get("type")
        if isinstance(metadata_type, type):
            if issubclass(metadata_type, float):
                var_type = "float"
            elif issubclass(metadata_type, str):
                var_type = "string"

        # Ensure we get string values for description and unit
        description = str(metadata["description"]) if metadata.get("description") else ""
        unit = str(metadata["unit"]) if metadata.get("unit") not in (None, "None") else None

        # Get comment if available
        comment = str(metadata["comment"]) if metadata.get("comment") else None

        variables.append(
            WeatherVariableMetadata(
                name=var_name,
                description=description,
                unit=unit,
                groups=groups,
                type=var_type,
                comment=comment,
            )
        )

    return sorted(variables, key=lambda x: x.name)
