# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import asyncio
import logging
import time
from collections import defaultdict, deque

from fastapi import Request
from fastapi.responses import JSONResponse

from app.config import settings

logger = logging.getLogger(__name__)


class RequestThrottler:
    """Throttles requests based on IP and time."""

    def __init__(
        self,
        rate_limit_per_minute: int = getattr(settings, "RATE_LIMIT_PER_MINUTE", 600),
        rate_limit_per_ip: int = getattr(settings, "RATE_LIMIT_PER_IP", 120),
        burst_limit: int = getattr(settings, "BURST_LIMIT", 8),
        cleanup_interval: int = getattr(settings, "THROTTLE_CLEANUP_INTERVAL", 10),
    ) -> None:
        self.rate_limit_per_minute = rate_limit_per_minute
        self.rate_limit_per_ip = rate_limit_per_ip
        self.burst_limit = burst_limit
        self._cleanup_interval = cleanup_interval

        # Increase deque sizes slightly to account for cleanup interval
        buffer_size = 1 + (cleanup_interval / 60)  # Buffer factor based on cleanup interval
        global_maxlen = int(rate_limit_per_minute * buffer_size)
        ip_maxlen = int(rate_limit_per_ip * buffer_size)

        # Initialize deques with calculated maxlen
        self.ip_requests: dict[str, deque[float]] = defaultdict(lambda: deque(maxlen=ip_maxlen))
        self.global_requests: deque[float] = deque(maxlen=global_maxlen)
        self.burst_windows: dict[str, deque[float]] = defaultdict(lambda: deque(maxlen=burst_limit))

        self._lock = asyncio.Lock()
        self._last_cleanup = time.time()

        logger.info(
            f"Initialized RequestThrottler with limits: "
            f"global_per_minute={rate_limit_per_minute}, "
            f"per_ip={rate_limit_per_ip}, "
            f"burst={burst_limit}, "
            f"cleanup_interval={cleanup_interval}s, "
            f"buffer_size={buffer_size:.2f}x"
        )

    def _should_cleanup(self, now: float) -> bool:
        """Check if cleanup is needed based on interval"""
        return now - self._last_cleanup > self._cleanup_interval

    def _cleanup_old_requests(self, now: float) -> None:
        """Remove requests older than 1 minute, but only if cleanup interval has passed"""
        if not self._should_cleanup(now):
            return

        minute_ago = now - 60
        second_ago = now - 1

        # cleanup global requests
        while self.global_requests and self.global_requests[0] < minute_ago:
            self.global_requests.popleft()

        # cleanup IP-specific requests
        for ip in list(self.ip_requests.keys()):
            # Cclean minute window
            while self.ip_requests[ip] and self.ip_requests[ip][0] < minute_ago:
                self.ip_requests[ip].popleft()

            # clean burst window
            while self.burst_windows[ip] and self.burst_windows[ip][0] < second_ago:
                self.burst_windows[ip].popleft()

            # remove IP entries if no recent requests
            if not self.ip_requests[ip]:
                del self.ip_requests[ip]
                del self.burst_windows[ip]

        self._last_cleanup = now

    async def check_request(self, request: Request) -> JSONResponse | None:
        """Check if request should be throttled"""
        client_ip = request.client.host if request.client else "unknown"
        now = time.time()

        # fast path: check burst limit without lock
        burst_window = self.burst_windows.get(client_ip, deque())
        if len(burst_window) >= self.burst_limit and burst_window[0] > now - 1:
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Too many requests",
                    "detail": "Burst limit exceeded. Please slow down.",
                    "retry_after": "1",
                },
                headers={"Retry-After": "1"},
            )

        async with self._lock:
            self._cleanup_old_requests(now)

            if len(self.global_requests) >= self.rate_limit_per_minute:
                return JSONResponse(
                    status_code=429,
                    content={
                        "error": "Too many requests",
                        "detail": "Global rate limit exceeded.",
                        "retry_after": "60",
                    },
                    headers={"Retry-After": "60"},
                )

            if len(self.ip_requests[client_ip]) >= self.rate_limit_per_ip:
                return JSONResponse(
                    status_code=429,
                    content={
                        "error": "Too many requests",
                        "detail": "IP-based rate limit exceeded.",
                        "retry_after": "60",
                    },
                    headers={"Retry-After": "60"},
                )

            # record request with O(1) operations
            self.global_requests.append(now)
            self.ip_requests[client_ip].append(now)
            self.burst_windows[client_ip].append(now)
            return None
