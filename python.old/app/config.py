# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import os
from typing import Any

from dotenv import load_dotenv
from pydantic_settings import BaseSettings

# load environment variables from .env file
load_dotenv(override=True)


class Settings(BaseSettings):
    # Version Info
    VERSION: str = os.getenv("VERSION", "0.1.0")
    BUILD_DATE: str = os.getenv("BUILD_DATE", "")
    GIT_COMMIT: str = os.getenv("GIT_COMMIT", "")

    # API Settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Zephyr Weather Service"
    API_TOKEN: str = os.getenv("API_TOKEN", "your-super-secret-token")

    # Documentation Auth
    DOCS_USERNAME: str = os.getenv("DOCS_USERNAME", "admin")
    DOCS_PASSWORD: str = os.getenv("DOCS_PASSWORD", "admin")

    # API Security Settings
    API_SECURITY_SCHEME: dict[str, Any] = {
        "bearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "Enter your Bearer token in the format: Bearer <token>",
        }
    }
    API_SECURITY_REQUIREMENTS: list[dict[str, list]] = [{"bearerAuth": []}]

    # Throttling Settings
    RATE_LIMIT_PER_MINUTE: int = int(os.getenv("RATE_LIMIT_PER_MINUTE", 600))
    RATE_LIMIT_PER_IP: int = int(os.getenv("RATE_LIMIT_PER_IP", 120))
    BURST_LIMIT: int = int(os.getenv("BURST_LIMIT", 8))

    # Redis Settings
    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", 6379))
    REDIS_DB: int = int(os.getenv("REDIS_DB", 0))
    LOCATION_TTL: int = int(os.getenv("LOCATION_TTL", 18000))  # 5 hours in seconds

    # InfluxDB Settings
    INFLUXDB_URL: str = os.getenv("INFLUXDB_URL", "http://localhost:8086")
    INFLUXDB_TOKEN: str = os.getenv("INFLUXDB_TOKEN", "your-token")
    INFLUXDB_ORG: str = os.getenv("INFLUXDB_ORG", "zephyr")
    INFLUXDB_BUCKET: str = os.getenv("INFLUXDB_BUCKET", "weather_data")

    # Visual Crossing API Settings
    VISUAL_CROSSING_API_KEY: str = os.getenv("VISUAL_CROSSING_API_KEY", "")
    VISUAL_CROSSING_BASE_URL: str = os.getenv(
        "VISUAL_CROSSING_BASE_URL",
        "https://weather.visualcrossing.com/VisualCrossingWebServices/rest/services/timeline",
    )

    # Service Settings
    COORDINATE_PRECISION: int = int(os.getenv("COORDINATE_PRECISION", 2))
    POLLING_INTERVAL: int = int(os.getenv("POLLING_INTERVAL", 900))  # 15 minutes in seconds
    INACTIVE_CLEANUP_DAYS: int = int(os.getenv("INACTIVE_CLEANUP_DAYS", 30))

    # Location Types and Variables
    LOCATION_TYPES: list[str] = ["solar", "wind", "consumption"]

    class Config:
        case_sensitive = True


settings = Settings()
