# Copyright © 2025 Emissium
# This file is part of the Emissium library.

"""
Weather variables and metadata.
"""

from enum import Enum


class WeatherGroup(str, Enum):
    """Groups of weather indicators by type and use case."""

    # Physical measurement groups
    TEMPERATURE = "temperature"
    HUMIDITY = "humidity"
    WIND = "wind"
    PRECIPITATION = "precipitation"
    ATMOSPHERE = "atmosphere"
    SOLAR = "solar"
    GENERAL = "general"

    # Height-specific measurements
    WIND_HEIGHT = "wind_height"
    SOLAR_RADIATION = "solar_radiation"

    # indicators relevant for heating, cooling, ventilation demand
    CONSUMPTION = "consumption"  # indicators relevant for heating, cooling, ventilation demand

    SOLAR_PROD = "solar_prod"  # indicators for photovoltaic generation and solar thermal systems

    WIND_ONSHORE = "wind_onshore"  # indicators for onshore wind generation
    WIND_OFFSHORE = "wind_offshore"  # indicators for offshore wind generation


# Type alias for variable metadata values
VariableMetadataValue = str | set[WeatherGroup] | None | type[float] | type[str]

# Define measurement units, descriptions and groups for all variables
VARIABLE_METADATA: dict[str, dict[str, VariableMetadataValue]] = {
    "temperature": {
        "unit": "°C",
        "description": "Ambient temperature",
        "groups": {
            WeatherGroup.TEMPERATURE,
            WeatherGroup.CONSUMPTION,
            WeatherGroup.WIND_OFFSHORE,
            WeatherGroup.WIND_ONSHORE,
        },
        "type": float,
    },
    "temperature_max": {
        "unit": "°C",
        "description": "Maximum temperature registered during the entire day",
        "groups": {WeatherGroup.TEMPERATURE},
        "type": float,
    },
    "temperature_min": {
        "unit": "°C",
        "description": "Minimum temperature registered during the entire day",
        "groups": {WeatherGroup.TEMPERATURE},
        "type": float,
    },
    "feels_like": {
        "unit": "°C",
        "description": "Perceived temperature (accounting for heat index or wind chill)",
        "groups": {WeatherGroup.TEMPERATURE, WeatherGroup.CONSUMPTION},
        "type": float,
    },
    "humidity": {
        "unit": "%",
        "description": "Relative humidity",
        "groups": {
            WeatherGroup.HUMIDITY,
            WeatherGroup.CONSUMPTION,
            WeatherGroup.WIND_OFFSHORE,
            WeatherGroup.WIND_ONSHORE,
        },
        "type": float,
    },
    "dew_point": {
        "unit": "°C",
        "description": "Temperature at which air becomes saturated",
        "groups": {WeatherGroup.HUMIDITY, WeatherGroup.CONSUMPTION},
        "type": float,
    },
    "wind_speed": {
        "unit": "km/h",
        "description": "Wind speed at 10m height",
        "groups": {WeatherGroup.WIND, WeatherGroup.WIND_ONSHORE, WeatherGroup.CONSUMPTION},
        "type": float,
    },
    "wind_gust": {
        "unit": "km/h",
        "description": "Instantaneous wind speed at 10m height",
        "groups": {WeatherGroup.WIND},
        "type": float,
    },
    "wind_direction": {
        "unit": "degrees",
        "description": "Wind direction at 10m height (0° = North, 90° = East)",
        "groups": {WeatherGroup.WIND},
        "type": float,
    },
    "precipitation": {
        "unit": "mm",
        "description": "Liquid-equivalent precipitation amount",
        "groups": {WeatherGroup.PRECIPITATION},
        "type": float,
    },
    "precipitation_probability": {
        "unit": "%",
        "description": "The likelihood of precipitation in percentage for the day",
        "groups": {WeatherGroup.PRECIPITATION},
        "type": float,
    },
    "precipitation_cover": {
        "unit": "%",
        "description": "Proportion of time with non-zero precipitation for the day",
        "groups": {WeatherGroup.PRECIPITATION},
        "type": float,
    },
    "precipitation_type": {
        "unit": None,
        "description": "Type of precipitation (rain, snow, freezingrain, ice)",
        "groups": {WeatherGroup.PRECIPITATION},
        "type": str,
    },
    "snow": {
        "unit": "cm",
        "description": "Snowfall amount",
        "groups": {WeatherGroup.PRECIPITATION, WeatherGroup.CONSUMPTION},
        "type": float,
    },
    "snow_depth": {
        "unit": "cm",
        "description": "Accumulated snow depth",
        "groups": {WeatherGroup.PRECIPITATION, WeatherGroup.CONSUMPTION},
        "type": float,
    },
    "visibility": {
        "unit": "km",
        "description": "Horizontal visibility distance",
        "groups": {WeatherGroup.ATMOSPHERE},
        "type": float,
    },
    "cloud_cover": {
        "unit": "%",
        "description": "Sky covered by clouds expressed as a percentage",
        "groups": {WeatherGroup.SOLAR, WeatherGroup.ATMOSPHERE},
        "type": float,
    },
    "solar_radiation": {
        "unit": "W/m²",
        "description": "Instantaneous solar radiation power",
        "groups": {WeatherGroup.SOLAR, WeatherGroup.SOLAR_RADIATION, WeatherGroup.SOLAR_PROD},
        "type": float,
    },
    "solar_energy": {
        "unit": "MJ/m²",
        "description": "Total solar energy that builds up over time period",
        "groups": {WeatherGroup.SOLAR},
        "type": float,
    },
    "uv_index": {
        "unit": None,
        "description": "Ultraviolet radiation intensity (0-10)",
        "groups": {WeatherGroup.SOLAR},
        "type": float,
    },
    "pressure": {
        "unit": "mb",
        "description": "Sea level atmospheric or barometric pressure",
        "groups": {WeatherGroup.ATMOSPHERE, WeatherGroup.WIND_ONSHORE, WeatherGroup.WIND_OFFSHORE},
        "type": float,
    },
    "conditions": {
        "unit": None,
        "description": "Text description of conditions",
        "groups": {WeatherGroup.GENERAL},
        "type": str,
    },
    "sunrise": {
        "unit": None,
        "description": "Time of sunrise",
        "groups": {WeatherGroup.SOLAR},
        "type": str,
    },
    "sunset": {
        "unit": None,
        "description": "Time of sunset",
        "groups": {WeatherGroup.SOLAR},
        "type": str,
    },
    "wind_speed_50m": {
        "unit": "km/h",
        "description": "Wind speed at 50m height",
        "groups": {WeatherGroup.WIND_HEIGHT, WeatherGroup.WIND_ONSHORE},
        "type": float,
    },
    "wind_direction_50m": {
        "unit": "degrees",
        "description": "Wind direction at 50m height",
        "groups": {WeatherGroup.WIND_HEIGHT, WeatherGroup.WIND_ONSHORE},
        "type": float,
    },
    "wind_speed_80m": {
        "unit": "km/h",
        "description": "Wind speed at 80m height",
        "groups": {WeatherGroup.WIND_HEIGHT, WeatherGroup.WIND_ONSHORE},
        "type": float,
    },
    "wind_direction_80m": {
        "unit": "degrees",
        "description": "Wind direction at 80m height",
        "groups": {WeatherGroup.WIND_HEIGHT, WeatherGroup.WIND_ONSHORE},
        "type": float,
    },
    "wind_speed_100m": {
        "unit": "km/h",
        "description": "Wind speed at 100m height",
        "groups": {WeatherGroup.WIND_HEIGHT, WeatherGroup.WIND_ONSHORE, WeatherGroup.WIND_OFFSHORE},
        "type": float,
    },
    "wind_direction_100m": {
        "unit": "degrees",
        "description": "Wind direction at 100m height",
        "groups": {WeatherGroup.WIND_HEIGHT, WeatherGroup.WIND_ONSHORE, WeatherGroup.WIND_OFFSHORE},
        "type": float,
    },
    "direct_normal_radiation": {
        "unit": "W/m²",
        "description": "Direct beam radiation perpendicular to sun",
        "comment": "Use this indicator for solar CSP generation.",
        "groups": {WeatherGroup.SOLAR, WeatherGroup.SOLAR_RADIATION, WeatherGroup.SOLAR_PROD},
        "type": float,
    },
    "diffuse_radiation": {
        "unit": "W/m²",
        "description": "Sky radiation that is scattered (not direct sunlight)",
        "groups": {WeatherGroup.SOLAR, WeatherGroup.SOLAR_RADIATION},
        "type": float,
    },
    "global_horizontal_radiation": {
        "unit": "W/m²",
        "description": "Total solar energy on a flat horizontal surface",
        "comment": "Use this indicator for generation from flat horizontal PV systems.",
        "groups": {WeatherGroup.SOLAR, WeatherGroup.SOLAR_RADIATION, WeatherGroup.SOLAR_PROD},
        "type": float,
    },
    "global_tilt_radiation": {
        "unit": "W/m²",
        "description": (
            "Solar radiation on a tilted surface — like a panel mounted on a sloped roof"
        ),
        "comment": "Use this indicator for generation from fixed-tilt solar panels.",
        "groups": {WeatherGroup.SOLAR, WeatherGroup.SOLAR_RADIATION, WeatherGroup.SOLAR_PROD},
        "type": float,
    },
    "sun_elevation": {
        "unit": "degrees",
        "description": "Sun angle above horizon calculated as the mean point of the period",
        "groups": {WeatherGroup.SOLAR},
        "type": float,
    },
}

# Map groups to their indicator tags
GROUP_TO_VARIABLES = {
    member.value: {
        tag
        for tag in VARIABLE_METADATA
        if (groups := VARIABLE_METADATA[tag].get("groups"))
        and isinstance(groups, set)
        and any(g == member for g in groups)
    }
    for member in WeatherGroup
}


def get_required_variables(location_types: list[str]) -> set[str]:
    """
    Get the set of required weather variables for given location types.

    Args:
        location_types: List of location types (e.g., ["solar", "wind"])

    Returns:
        Set of required variable names
    """
    required_vars = set()
    for loc_type in location_types:
        if loc_type in GROUP_TO_VARIABLES:
            required_vars.update(GROUP_TO_VARIABLES[loc_type])
    return required_vars


def get_variable_metadata(variable_name: str) -> dict[str, VariableMetadataValue]:
    """
    Get metadata for a specific weather variable.

    Args:
        variable_name: Name of the weather variable

    Returns:
        Dictionary with unit, description and groups
    """
    return VARIABLE_METADATA.get(
        variable_name, {"unit": "unknown", "description": "Unknown variable", "groups": set()}
    )
