# Copyright © 2025 Emissium
# This file is part of the Emissium library.

"""
Note: this module is currently not used.
"""

from app.core.variables import get_required_variables


class WeatherNormalizer:
    """Handles normalization of weather data across different sources and formats."""

    @staticmethod
    def normalize_temperature(value: float, source_unit: str | None = "K") -> float:
        """Normalize temperature to Celsius."""
        if not source_unit:
            return value  # assume Celsius
        if source_unit == "K":
            return value - 273.15
        elif source_unit == "F":
            return (value - 32) * 5 / 9
        return value  # assume Celsius

    @staticmethod
    def normalize_wind_speed(
        value: float, source_unit: str | None = "m/s", height: float = 10.0
    ) -> float:
        """
        Normalize wind speed to m/s at 10m height using the power law profile.

        Args:
            value: Wind speed value
            source_unit: Unit of the input value ('m/s', 'km/h', 'mph')
            height: Height of the measurement in meters
        """
        if not source_unit:
            return value  # assume m/s

        # first convert to m/s
        if source_unit == "km/h":
            value = value / 3.6
        elif source_unit == "mph":
            value = value * 0.44704

        # if height is different from 10m, adjust using power law profile
        if height != 10.0:
            # alpha coefficient (roughness, typically 0.143 for open land)
            alpha = 0.143
            value = value * (10.0 / height) ** alpha

        return value

    @staticmethod
    def normalize_pressure(value: float, source_unit: str | None = "hPa") -> float:
        """Normalize pressure to hPa (hectopascals)."""
        if not source_unit:
            return value  # assume hPa
        if source_unit == "Pa":
            return value / 100
        elif source_unit == "atm":
            return value * 1013.25
        elif source_unit == "mmHg":
            return value * 1.33322
        return value  # assume hPa

    @staticmethod
    def normalize_solar_radiation(value: float, source_unit: str | None = "W/m²") -> float:
        """Normalize solar radiation to W/m²."""
        if not source_unit:
            return value  # assume W/m²
        if source_unit == "kW/m²":
            return value * 1000
        elif source_unit == "cal/cm²/min":  # langley per minute
            return value * 697.333
        return value  # assume W/m²

    @staticmethod
    def validate_ranges(data: dict[str, float]) -> dict[str, float]:
        """
        Validate and clean weather data values against physical limits.
        Returns the original value if within limits, otherwise returns 0.0.
        """
        limits = {
            "temperature": (-90.0, 60.0),  # °C (world records)
            "humidity": (0.0, 100.0),  # %
            "wind_speed": (0.0, 150.0),  # m/s (world records ~103 m/s)
            "wind_direction": (0.0, 360.0),  # degrees
            "pressure": (870.0, 1090.0),  # hPa (world records)
            "ghi": (0.0, 1361.0),  # W/m² (solar constant)
            "dni": (0.0, 1361.0),  # W/m² (solar constant)
            "precipitation": (0.0, 1000.0),  # mm (reasonable hourly limit)
            "air_density": (0.9, 1.4),  # kg/m³ (reasonable range)
        }

        validated = {}
        for var, value in data.items():
            if var in limits:
                min_val, max_val = limits[var]
                validated[var] = value if min_val <= value <= max_val else 0.0
            else:
                validated[var] = value
        return validated

    @classmethod
    def normalize_weather_data(
        cls,
        data: dict[str, float],
        location_types: list[str],
        source_units: dict[str, str] | None = None,
    ) -> dict[str, float]:
        """
        Normalize weather data based on location types and source units.

        Args:
            data: Raw weather data
            location_types: List of location types to determine required variables
            source_units: Dictionary mapping variables to their source units
        """
        if source_units is None:
            source_units = {}

        required_vars = get_required_variables(location_types)
        normalized = {}

        # normalize each required variable
        for var in required_vars:
            if var not in data:
                continue

            value = data[var]
            unit = source_units.get(var)

            if var == "temperature":
                normalized[var] = cls.normalize_temperature(value, unit)
            elif var == "wind_speed":
                height = data.get("measurement_height", 10.0)
                normalized[var] = cls.normalize_wind_speed(value, unit, height)
            elif var in ["ghi", "dni"]:
                normalized[var] = cls.normalize_solar_radiation(value, unit)
            elif var == "pressure":
                normalized[var] = cls.normalize_pressure(value, unit)
            else:
                normalized[var] = value

        # validate ranges
        normalized = cls.validate_ranges(normalized)

        return normalized
