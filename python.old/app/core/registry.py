# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import logging
from datetime import UTC, datetime

from app.config import settings
from app.models.location import Location, LocationCreate

logger = logging.getLogger(__name__)


class LocationRegistry:
    def __init__(self) -> None:
        self.precision = settings.COORDINATE_PRECISION

    def _normalize_coordinate(self, coord: float) -> float:
        """Normalize a coordinate to the specified precision."""
        return round(coord, self.precision)

    def _generate_location_id(self, lat: float, lon: float) -> str:
        """Generate a unique location identifier from coordinates."""
        norm_lat = self._normalize_coordinate(lat)
        norm_lon = self._normalize_coordinate(lon)
        return f"{norm_lat}:{norm_lon}"

    async def register_location(self, location: LocationCreate) -> Location:
        """Register a new location."""
        # normalize coordinates
        normalized_lat = self._normalize_coordinate(location.latitude)
        normalized_lon = self._normalize_coordinate(location.longitude)

        # generate location ID
        location_id = self._generate_location_id(location.latitude, location.longitude)

        # create full location object
        registered_location = Location(
            id=location_id,
            latitude=location.latitude,
            longitude=location.longitude,
            normalized_lat=normalized_lat,
            normalized_lon=normalized_lon,
            types=location.types,
            created_at=datetime.now(UTC),
        )

        logger.debug(f"Successfully registered location with ID: {location_id}")

        return registered_location
