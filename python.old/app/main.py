# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import logging
import secrets
from collections.abc import AsyncGenerator, Awaitable, Callable
from contextlib import asynccontextmanager
from typing import Annotated

from fastapi import Depends, FastAPI, HTTPException, Security
from fastapi.requests import Request
from fastapi.responses import Response
from fastapi.security import (
    HTTPAuthorizationCredentials,
    HTTPBasic,
    HTTPBasicCredentials,
    HTTPBearer,
)

from app.api.endpoints import health, locations, weather
from app.api.middleware.throttling import RequestThrottler
from app.config import settings

# configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger(__name__)

security = HTTPBearer()
basic_auth = HTTPBasic()
throttler = RequestThrottler()


def verify_docs_credentials(
    credentials: Annotated[HTTPBasicCredentials, Depends(basic_auth)],
) -> None:
    is_correct_username = secrets.compare_digest(
        credentials.username.encode(), settings.DOCS_USERNAME.encode()
    )
    is_correct_password = secrets.compare_digest(
        credentials.password.encode(), settings.DOCS_PASSWORD.encode()
    )

    if not (is_correct_username and is_correct_password):
        raise HTTPException(
            status_code=401,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )


async def verify_token(
    credentials: Annotated[HTTPAuthorizationCredentials, Security(security)],
) -> None:
    if credentials.credentials != settings.API_TOKEN:
        raise HTTPException(
            status_code=401,
            detail="Invalid authentication token",
            headers={"WWW-Authenticate": "Bearer"},
        )


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    # Startup
    logger.info("Starting up FastAPI application")
    yield
    # Shutdown
    logger.info("Shutting down FastAPI application")


def create_application() -> FastAPI:
    application = FastAPI(
        title=settings.PROJECT_NAME,
        openapi_url=f"{settings.API_V1_STR}/openapi.json",
        docs_url=f"{settings.API_V1_STR}/docs",
        redoc_url=f"{settings.API_V1_STR}/redoc",
        lifespan=lifespan,
        docs_auth=verify_docs_credentials,
        redoc_auth=verify_docs_credentials,
        openapi_auth=verify_docs_credentials,
        openapi_tags=[
            {"name": "locations", "description": "Location management operations"},
            {"name": "weather", "description": "Weather data operations"},
            {"name": "health", "description": "System health and status"},
        ],
        swagger_ui_parameters={"defaultModelsExpandDepth": -1},
        swagger_ui_init_oauth={},
        openapi_extra={
            "info": {
                "x-logo": {
                    "url": "https://www.venturekick.ch/demandit/files/M_BB941CC4DCEF687AD98/dms//Image/logo_total_background.png"
                }
            },
            "security": settings.API_SECURITY_REQUIREMENTS,
            "components": {"securitySchemes": settings.API_SECURITY_SCHEME},
        },
    )

    # add throttling middleware
    @application.middleware("http")
    async def throttle_requests(
        request: Request, call_next: Callable[[Request], Awaitable[Response]]
    ) -> Response:
        # skip throttling for documentation endpoints
        if request.url.path.endswith(("/docs", "/redoc", "/openapi.json")):
            return await call_next(request)

        throttle_response = await throttler.check_request(request)
        if throttle_response is not None:
            return throttle_response
        return await call_next(request)

    # include routers with authentication
    application.include_router(
        locations.router,
        prefix=settings.API_V1_STR,
        tags=["locations"],
        dependencies=[Depends(verify_token)],
    )
    application.include_router(
        weather.router,
        prefix=settings.API_V1_STR,
        tags=["weather"],
        dependencies=[Depends(verify_token)],
    )
    application.include_router(
        health.router,
        prefix=settings.API_V1_STR,
        tags=["health"],
        dependencies=[Depends(verify_token)],
    )

    return application


app = create_application()
