# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import asyncio
import logging
from datetime import UTC, datetime, timedelta

from app.config import settings
from app.services.influx import influx_service
from app.services.redis import redis_service
from app.workers.fetch_weather import WeatherFetcher

logger = logging.getLogger(__name__)


class TaskScheduler:
    """Manages and schedules background tasks for the weather service."""

    def __init__(self) -> None:
        self.weather_fetcher = WeatherFetcher()
        self.cleanup_interval = timedelta(days=10)  # run cleanup once per day
        self.last_cleanup = datetime.now(UTC)

    async def _should_run_cleanup(self) -> bool:
        """Check if it's time to run cleanup tasks."""
        now = datetime.now(UTC)
        if now - self.last_cleanup >= self.cleanup_interval:
            self.last_cleanup = now
            return True
        return False

    async def _cleanup_inactive_data(self) -> None:
        """Run cleanup tasks for inactive location data."""
        try:
            logger.info("Starting cleanup of inactive location data")
            await influx_service.cleanup_inactive_locations()
            logger.info("Completed cleanup of inactive location data")
        except Exception as e:
            logger.error(f"Error during cleanup: {e!s}")

    async def _run_weather_updates(self) -> None:
        """Run weather data updates for active locations."""
        try:
            await self.weather_fetcher.process_active_locations()
        except Exception as e:
            logger.error(f"Error during weather updates: {e!s}")

    async def _log_metrics(self) -> None:
        """Log system metrics and statistics."""
        try:
            active_locations = await redis_service.get_active_locations()
            logger.info(
                f"System metrics - Active locations: {len(active_locations)}, "
                f"Last cleanup: {self.last_cleanup.isoformat()}"
            )
        except Exception as e:
            logger.error(f"Error logging metrics: {e!s}")

    async def run(self) -> None:
        """Main scheduler loop."""
        logger.info("Starting task scheduler")

        while True:
            try:
                # run weather updates
                await self._run_weather_updates()

                # check if cleanup is needed
                if await self._should_run_cleanup():
                    await self._cleanup_inactive_data()

                # log metrics
                await self._log_metrics()

            except Exception as e:
                logger.error(f"Error in scheduler loop: {e!s}")

            # wait for next interval
            await asyncio.sleep(settings.POLLING_INTERVAL)


async def run_scheduler() -> None:
    """Entry point for running the scheduler."""
    scheduler = TaskScheduler()
    await scheduler.run()


if __name__ == "__main__":
    # configure logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    # run scheduler
    asyncio.run(run_scheduler())
