# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import asyncio
import logging
from datetime import UTC, datetime

from app.config import settings
from app.services.influx import influx_service
from app.services.redis import redis_service
from app.services.weather_manager import weather_manager

logger = logging.getLogger(__name__)


class WeatherFetcher:
    def __init__(self) -> None:
        self.polling_interval = settings.POLLING_INTERVAL

    async def fetch_location_weather(self, location_id: str, location_data: dict) -> None:
        try:
            # fetch current weather
            weather_data = await weather_manager.get_recent_weather(
                location_data["latitude"],
                location_data["longitude"],
                location_types=location_data["types"],
            )

            if weather_data:
                # store in InfluxDB using batch method
                await influx_service.store_weather_data(location_id, weather_data)

                # cache in Redis
                await redis_service.cache_recent(location_id, weather_data)

                # update last polled timestamp
                location_data["last_polled"] = datetime.now(UTC)
                await redis_service.add_active_location(location_id, location_data)

                logger.info(f"Successfully updated weather data for location {location_id}")
            else:
                logger.warning(f"No weather data received for location {location_id}")

        except Exception as e:
            logger.error(f"Error fetching weather for location {location_id}: {e!s}")

    async def fetch_location_forecast(self, location_id: str, location_data: dict) -> None:
        try:
            # fetch forecast data
            forecast_data = await weather_manager.get_forecast_weather(
                location_data["latitude"],
                location_data["longitude"],
                location_types=location_data["types"],
            )

            if forecast_data:
                # store in InfluxDB using batch method
                await influx_service.store_weather_data(location_id, forecast_data)

                # cache forecast data in Redis
                await redis_service.cache_forecast(location_id, forecast_data)

                logger.info(f"Successfully updated forecast data for location {location_id}")
            else:
                logger.warning(f"No forecast data received for location {location_id}")

        except Exception as e:
            logger.error(f"Error fetching forecast for location {location_id}: {e!s}")

    async def process_active_locations(self) -> None:
        try:
            # get all active locations
            active_locations = await redis_service.get_active_locations()

            if not active_locations:
                logger.info("No active locations to process")
                return

            # process each location
            tasks = []
            for location_id in active_locations:
                location_data = await redis_service.get_location(location_id)
                if location_data:
                    tasks.append(self.fetch_location_weather(location_id, location_data))
                    tasks.append(self.fetch_location_forecast(location_id, location_data))

            # run all tasks concurrently
            await asyncio.gather(*tasks)

        except Exception as e:
            logger.error(f"Error processing active locations: {e!s}")

    async def run(self) -> None:
        logger.info("Starting weather fetcher worker")
        while True:
            try:
                await self.process_active_locations()
            except Exception as e:
                logger.error(f"Error in weather fetcher loop: {e!s}")

            # wait for next polling interval
            await asyncio.sleep(self.polling_interval)


async def run_weather_fetcher() -> None:
    fetcher = WeatherFetcher()
    await fetcher.run()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(run_weather_fetcher())
