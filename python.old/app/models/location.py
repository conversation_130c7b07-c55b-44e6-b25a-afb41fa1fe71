# Copyright © 2025 Emissium
# This file is part of the Emissium library.

from datetime import UTC, datetime
from typing import ClassVar

from pydantic import BaseModel, Field, field_validator

from app.core.variables import WeatherGroup


class LocationBase(BaseModel):
    """Base model for location data."""

    latitude: float = Field(..., ge=-90, le=90)
    longitude: float = Field(..., ge=-180, le=180)
    types: list[str] = Field(default=[WeatherGroup.CONSUMPTION.value])

    @field_validator("types")
    @classmethod
    def validate_types(cls, v: list[str]) -> list[str]:
        valid_types = [t.value for t in WeatherGroup]
        for t in v:
            if t not in valid_types:
                raise ValueError(f"Invalid location type: {t}")
        return v


class LocationCreate(LocationBase):
    """Model for creating a new location."""

    pass


class Location(LocationBase):
    """Model for a location."""

    id: str
    normalized_lat: float
    normalized_lon: float
    last_polled: datetime | None = None
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC))

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "id": "48.86:2.35",
                "latitude": 48.8566,
                "longitude": 2.3522,
                "normalized_lat": 48.86,
                "normalized_lon": 2.35,
                "types": ["consumption", "solar"],
                "last_polled": "2025-01-01T12:00:00",
                "created_at": "2025-01-01T00:00:00",
            }
        }


if __name__ == "__main__":
    from datetime import UTC

    location = LocationCreate(
        latitude=48.8566,
        longitude=2.3522,
        types=["consumption", "solar"],
    )
    print(location.model_dump())

    location = Location(
        id="48.86:2.35",
        latitude=48.8566,
        longitude=2.3522,
        normalized_lat=48.86,
        normalized_lon=2.35,
        types=["consumption", "solar"],
        last_polled=datetime.now(UTC),
        created_at=datetime.now(UTC),
    )
    print(location.model_dump())
