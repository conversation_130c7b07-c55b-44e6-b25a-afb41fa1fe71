# Copyright © 2025 Emissium
# This file is part of the Emissium library.

from typing import Annotated

from pydantic import BaseModel, Field


class HealthStatus(BaseModel):
    status: Annotated[str, Field(description="Overall service status")]
    redis: Annotated[bool, <PERSON>(description="Redis connection status")]
    influxdb: Annotated[bool, Field(description="InfluxDB connection status")]

    model_config = {
        "json_schema_extra": {
            "examples": [{"status": "healthy", "redis": True, "influxdb": True}]
        }
    }


class SystemStatus(BaseModel):
    active_locations: Annotated[
        int, Field(description="Number of currently active locations")
    ]
    polling_interval_minutes: Annotated[
        int, Field(description="Current polling interval in minutes")
    ]
    last_poll_time: Annotated[
        str | None, Field(description="Timestamp of last successful poll")
    ] = None
    worker_status: Annotated[str, Field(description="Status of the worker process")]

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "active_locations": 42,
                    "polling_interval_minutes": 15,
                    "last_poll_time": "2024-03-27T10:00:00Z",
                    "worker_status": "running",
                }
            ]
        }
    }


class VersionInfo(BaseModel):
    version: Annotated[str, Field(description="API version")]
    build_date: Annotated[str, Field(description="Build timestamp")]
    git_commit: Annotated[str, Field(description="Git commit hash")]
    python_version: Annotated[str, Field(description="Python version")]

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "version": "1.0.0",
                    "build_date": "2024-03-27T10:00:00Z",
                    "git_commit": "abc123def",
                    "python_version": "3.12.1",
                }
            ]
        }
    }
