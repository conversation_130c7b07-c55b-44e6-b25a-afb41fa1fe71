# Copyright © 2025 Emissium
# This file is part of the Emissium library.

from typing import ClassVar, Self, TypedDict

from pydantic import BaseModel, Field, model_validator

from app.core.variables import get_required_variables
from app.services.weather_providers.types import Indicator, Resolution


class ResolutionDataSerialized(TypedDict):
    """Type for data at a specific time resolution."""

    timestamps: list[str]  # ISO 8601 (YYYY-MM-DDTHH:MM:SS or YYYY-MM-DD)
    indicators: dict[Indicator, list[float | str | None]]


WeatherDataWithResolutionSerialized = dict[Resolution, ResolutionDataSerialized]


class WeatherData(BaseModel):
    """Weather data organized by time resolution."""

    location_id: str = Field(..., description="Location identifier")
    location_types: list[str] = Field(..., description="Types of measurements for this location")
    data: WeatherDataWithResolutionSerialized = Field(
        default_factory=dict, description="Weather data organized by time resolution"
    )

    @model_validator(mode="after")
    def validate_required_fields(self: Self) -> Self:
        """Validate that all required fields for the specified location types are present."""
        required_vars = get_required_variables(self.location_types)

        for resolution_data in self.data.values():
            for var in required_vars:
                if var not in resolution_data["indicators"]:
                    raise ValueError(
                        f"Field {var} is required for location types {self.location_types}"
                    )

        return self

    class Config:
        json_schema_extra: ClassVar[dict] = {
            "example": {
                "location_id": "48.86:2.35",
                "location_types": ["solar", "wind"],
                "data": {
                    "15min": {
                        "timestamps": [
                            "2025-01-01T12:00:00",
                            "2025-01-01T12:15:00",
                            "2025-01-01T12:30:00",
                        ],
                        "indicators": {
                            "temperature": [20.5, 21.0, 21.2],
                            "wind_speed": [18.7, 19.2, 18.9],
                        },
                    },
                    "hourly": {
                        "timestamps": ["2025-01-01T12:00:00", "2025-01-01T13:00:00"],
                        "indicators": {"temperature": [20.5, 21.5], "wind_speed": [18.7, 19.5]},
                    },
                },
            }
        }
