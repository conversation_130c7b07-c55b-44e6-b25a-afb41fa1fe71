# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import logging
from datetime import date, datetime
from enum import Enum
from typing import ClassVar, TypedDict, cast

from app.config import settings
from app.core.variables import VARIABLE_METADATA
from app.services.weather_providers.api_client import BaseWeatherClient
from app.services.weather_providers.types import Indicator, Resolution, WeatherResults
from app.services.weather_providers.visual_crossing import VisualCrossingClient

logger = logging.getLogger(__name__)


class WeatherProviderType(Enum):
    VISUAL_CROSSING = "visual_crossing"
    # add other providers here as needed


class WeatherClientFactory:
    _providers: ClassVar[dict[WeatherProviderType, type[BaseWeatherClient]]] = {
        WeatherProviderType.VISUAL_CROSSING: VisualCrossingClient,
        # add other mappings here
    }

    @classmethod
    def create(
        cls,
        provider_type: WeatherProviderType,
        **kwargs,  # noqa: ANN003
    ) -> BaseWeatherClient:
        provider_class = cls._providers.get(provider_type)
        if not provider_class:
            raise ValueError(f"Unsupported provider type: {provider_type}")
        return provider_class(**kwargs)


class ResolutionData(TypedDict):
    """Type for data at a specific time resolution."""

    timestamps: list[datetime] | list[date]
    indicators: dict[Indicator, list[float | str | None]]


WeatherDataWithResolution = dict[Resolution, ResolutionData]


class WeatherProvidersManager:
    """Manager for multiple weather data providers."""

    def __init__(self) -> None:
        self.providers: dict[WeatherProviderType, BaseWeatherClient] = {}
        self._initialize_providers()

    def _initialize_providers(self) -> None:
        """Initialize all supported weather providers."""
        # initialize Visual Crossing provider
        self.providers[WeatherProviderType.VISUAL_CROSSING] = WeatherClientFactory.create(
            WeatherProviderType.VISUAL_CROSSING,
            api_tokens=[settings.VISUAL_CROSSING_API_KEY],
        )
        # additional providers can be initialized here as needed

    def supports(self, indicators: list[str]) -> bool:
        """Check if any provider supports the given indicators."""
        return any(indicator in VARIABLE_METADATA for indicator in indicators)

    def process_results(self, result: WeatherResults) -> WeatherDataWithResolution:
        """Process the results from the weather providers."""
        data: WeatherDataWithResolution = {}

        for resolution in result:
            if not result[resolution]:
                continue

            # collect unique timestamps from all indicators
            all_timestamps = set()
            for indicator_data in result[resolution].values():
                all_timestamps.update(indicator_data["times"])

            # sort timestamps to maintain chronological order
            sorted_timestamps = sorted(all_timestamps)

            # initialize resolution data
            resolution_key = cast(Resolution, "15min" if resolution == "minute" else resolution)
            data[resolution_key] = {"timestamps": sorted_timestamps, "indicators": {}}

            # collect all indicators for this resolution
            for indicator in result[resolution]:
                if indicator not in VARIABLE_METADATA:
                    continue

                # create a mapping of timestamps to values for this indicator
                timestamp_to_value = dict(
                    zip(
                        result[resolution][indicator]["times"],
                        result[resolution][indicator]["values"],
                        strict=True,
                    )
                )

                # create list of values aligned with the sorted timestamps
                aligned_values = [timestamp_to_value.get(ts) for ts in sorted_timestamps]
                data[resolution_key]["indicators"][indicator] = aligned_values

        return data

    async def get_recent_weather(
        self, latitude: float, longitude: float, location_types: list[str]
    ) -> WeatherDataWithResolution | None:
        """Get current weather data for a location using the default provider."""
        # currently using Visual Crossing as default provider
        provider = self.providers[WeatherProviderType.VISUAL_CROSSING]
        try:
            result: WeatherResults | None = await provider.fetch_recent(
                lat=latitude,
                lon=longitude,
                groups=location_types,
            )
            if not result:
                return None

            return self.process_results(result)
        except Exception as e:
            logger.error(f"Error getting current weather: {e!s}")
            return None

    async def get_forecast_weather(
        self, latitude: float, longitude: float, location_types: list[str]
    ) -> WeatherDataWithResolution | None:
        """Get weather forecast for a location using the default provider."""
        # currently using Visual Crossing as default provider
        provider = self.providers[WeatherProviderType.VISUAL_CROSSING]
        try:
            result: WeatherResults | None = await provider.fetch_forecast(
                lat=latitude,
                lon=longitude,
                groups=location_types,
            )
            if not result:
                return None

            return self.process_results(result)
        except Exception as e:
            logger.error(f"Error getting forecast: {e!s}")
            return None

    async def get_historical_weather(
        self,
        latitude: float,
        longitude: float,
        start: datetime,
        end: datetime,
        location_types: list[str],
    ) -> WeatherDataWithResolution | None:
        """Get historical weather data for a location using the default provider."""
        # currently using Visual Crossing as default provider
        provider = self.providers[WeatherProviderType.VISUAL_CROSSING]
        try:
            result: WeatherResults | None = await provider.fetch_historical(
                lat=latitude,
                lon=longitude,
                start=start,
                end=end,
                groups=location_types,
            )
            if not result:
                return None

            return self.process_results(result)
        except Exception as e:
            logger.error(f"Error getting historical weather: {e!s}")
            return None


# initialize the manager
weather_manager = WeatherProvidersManager()
