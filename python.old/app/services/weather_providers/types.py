# Copyright © 2025 Emissium
# This file is part of the Emissium library.

from datetime import date, datetime
from typing import Any, Literal, NewType, TypedDict

Indicator = NewType("Indicator", str)
Resolution = Literal["15min", "hourly", "daily"]


class MinuteTimeSeriesData(TypedDict):
    """Time series data for minute-resolution indicators."""

    values: list[Any]
    times: list[datetime]


class HourlyTimeSeriesData(TypedDict):
    """Time series data for hour-resolution indicators."""

    values: list[Any]
    times: list[datetime]


class DailyTimeSeriesData(TypedDict):
    """Time series data for day-resolution indicators."""

    values: list[Any]
    times: list[date]


class WeatherResults(TypedDict):
    """Weather data organized by time resolution."""

    minute: dict[Indicator, MinuteTimeSeriesData]  # minute-resolution indicators
    hourly: dict[Indicator, HourlyTimeSeriesData]  # hour-resolution indicators
    daily: dict[Indicator, DailyTimeSeriesData]  # day-resolution indicators
