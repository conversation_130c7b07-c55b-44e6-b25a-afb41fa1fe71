# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import logging
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any

import aiohttp

from app.services.weather_providers.client_requests.message_types import MessageTypes
from app.services.weather_providers.client_requests.rate_limits import RateLimiter
from app.services.weather_providers.types import WeatherResults
from app.services.weather_providers.utils.errors import APIConnectionError, ApiRequestError, Err


class BaseWeatherClient(ABC):
    outbound_rate_limiter: RateLimiter
    message_type: MessageTypes
    log: logging.Logger
    session: aiohttp.ClientSession | None = None
    _initialized: bool = False

    def __init__(self, message_type: MessageTypes, log: logging.Logger) -> None:
        self.message_type = message_type
        self.outbound_rate_limiter = RateLimiter()
        self.log = log

    async def __aenter__(self) -> "BaseWeatherClient":
        """Initialize async resources."""
        await self._ensure_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:  # noqa: ANN001
        """Cleanup async resources."""
        await self._cleanup_session()

    async def _ensure_session(self) -> None:
        """Ensure a session exists and is active."""
        if not self.session or self.session.closed:
            self.session = aiohttp.ClientSession()
            self._initialized = True

    async def _cleanup_session(self) -> None:
        """Clean up the session if it exists."""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None
            self._initialized = False

    @abstractmethod
    async def fetch_historical(
        self,
        lat: float,
        lon: float,
        start: datetime,
        end: datetime,
        indicators: list[str] | None = None,
        groups: list[str] | None = None,
    ) -> WeatherResults:
        """Fetch historical data from the API."""
        pass

    @abstractmethod
    async def fetch_forecast(
        self,
        lat: float,
        lon: float,
        indicators: list[str] | None = None,
        groups: list[str] | None = None,
    ) -> WeatherResults:
        """Fetch forecast data from the API."""
        pass

    @abstractmethod
    async def fetch_recent(
        self,
        lat: float,
        lon: float,
        indicators: list[str] | None = None,
        groups: list[str] | None = None,
    ) -> WeatherResults:
        """Fetch recent data from the API."""
        pass

    async def send_request(self, url: str, **params) -> dict[str, Any]:  # noqa: ANN003
        """
        Send an async HTTP request.
        Automatically initializes session if needed.
        """
        # Ensure we have an active session
        await self._ensure_session()
        if self.session is None:  # this should never happen due to _ensure_session
            raise RuntimeError("Failed to initialize session")

        message_type = MessageTypes(self.message_type).name

        if not self.outbound_rate_limiter.process_msg_and_check(self.message_type):
            message = f"Rate limiting API client. Message type: {message_type}"
            self.log.debug(message)
            raise ApiRequestError(Err.RATE_LIMITED_CLIENT, message)

        try:
            async with self.session.get(url, **params) as response:
                response.raise_for_status()
                return await response.json()
        except aiohttp.ClientError as exc:
            raise APIConnectionError(message_type, str(exc)) from exc

    def __del__(self) -> None:
        """Ensure resources are cleaned up on deletion."""
        if self._initialized and self.session and not self.session.closed:
            self.log.warning("ApiClient was not properly closed. Resources may leak.")
