# Copyright © 2022 Emissium
# This file is part of the Emissium library.

from __future__ import annotations

import logging
import re

log = logging.getLogger(__name__)


class ApiKey:
    """
    Custom parameter type to validate user input for any token.
    """

    def __init__(self, token: str, name: str, pattern: str) -> None:
        self.token = token
        self.name = name
        self.pattern = pattern

    def validate(self) -> bool:
        """
        Validate the token by checking the length and the characters.
        """
        match = re.match(self.pattern, self.token)  # None if a match is not found
        is_valid = match is not None

        if not is_valid:
            m = re.search(r"\{(\w+)\}", self.pattern)
            if m:
                log.warning(f"{self.token} is not a {m.group(1)}-character string'.")
            else:
                log.warning(f"{self.token} is not valid for {self.name}.")

        return is_valid


class VCApiKey(ApiKey):
    """
    VISUAL CROSSING API token.
    """

    def __init__(self, token: str) -> None:
        super().__init__(token, "vc-api-key", r"[0-9A-Z]{25}")
