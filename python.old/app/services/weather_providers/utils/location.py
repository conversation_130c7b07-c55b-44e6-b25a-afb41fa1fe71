# Copyright © 2022 Emissium
# This file is part of the Emissium library.

import dataclasses

from app.services.weather_providers.utils.errors import ApiRequestError, Err


@dataclasses.dataclass(frozen=True)
class Location:
    name: str
    country: str
    timezone: str
    city: str | None = None
    coordinates: tuple[float, float] | None = None

    def __str__(self) -> str:
        if self.city is not None:
            # weather station --> given as city name, country code (e.g. 'Rome,IT')
            return f"{self.city},{self.country}"

        if self.coordinates is None:
            raise ApiRequestError(Err.INVALID_LOCATION)

        return f"{self.coordinates[0]},{self.coordinates[1]}"
