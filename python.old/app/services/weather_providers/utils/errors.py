# Copyright © 2025 Emissium
# This file is part of the Emissium library.

from enum import Enum, auto


class Err(Enum):
    """Error codes for API requests."""

    INVALID_INDICATOR = auto()
    INVALID_INDICATORS = auto()  # no valid indicators or groups provided
    INVALID_DATES = auto()
    INVALID_LOCATION = auto()
    INVALID_REQUEST = auto()
    RATE_LIMITED_CLIENT = auto()
    INVALID_API_KEY = auto()  # Added for API key validation


class APIConnectionError(Exception):
    """
    Raised when a query to system_definition returns no matching data.
    """

    def __init__(self, api_name: str, message: str = "") -> None:
        super().__init__(f"Failed to connect to {api_name}: {message}")
        self.api_name = api_name
        self.message = message


class ApiRequestError(Exception):
    """
    Exception to denote that something went wrong when making the request.
    """

    def __init__(self, code: Err, message: str = "", info: str = "") -> None:
        super().__init__(f"Error code: {code.name}")
        self.code = code
        self.message = message
        self.info = info

    def __str__(self) -> str:
        return self.message
