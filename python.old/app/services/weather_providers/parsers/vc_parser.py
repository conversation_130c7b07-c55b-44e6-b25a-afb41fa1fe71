# Copyright © 2025 Emissium
# This file is part of the Emissium library.

from __future__ import annotations

from datetime import UTC, date, datetime, timedelta, tzinfo
from typing import Any, TypedDict
from zoneinfo import ZoneInfo

from app.services.weather_providers.settings.vc_settings import (
    VC_DAILY_TAGS_MAP,
    VC_HOURLY_TAGS_MAP,
    VC_MINUTE_TAGS_MAP,
    VC_QUERY_ELEMENTS_MAP,
    TimeAlignment,
)

# In the Arctic region, the areas north of the Arctic Circle (at 66.5 degrees North latitude)
# experience at least one day of 24-hour darkness during the winter solstice, which occurs around
# December 21st. The same zones experience at least one day of 24-hour daylight during
# the summer solstice, which occurs around June 21st.
LIMIT_ARCTIC_LAT = 66.5


class ParsedWeather(TypedDict):
    """Weather data for a specific time resolution."""

    minute: dict[datetime, dict[str, Any]]  # minute-resolution data
    hourly: dict[datetime, dict[str, Any]]  # hour-resolution data
    daily: dict[date, dict[str, Any]]  # day-resolution data
    location: tuple[float, float]  # latitude, longitude
    timezone: str  # timezone name
    query_cost: int  # API query cost


def _get_datetime_data(
    data: dict[datetime, dict[str, Any]],
    indicator: str,
) -> tuple[list[Any], list[datetime]]:
    """
    Get datetime-resolution data for an indicator.
    """
    indicator_series = []
    time_series = []

    for time_point, values in sorted(data.items()):
        if indicator in values:
            indicator_series.append(values[indicator])
            time_series.append(time_point)

    return indicator_series, time_series


def get_minute_data(
    data: dict[datetime, dict[str, Any]],
    indicator: str,
) -> tuple[list[Any], list[datetime]]:
    """
    Get minute-resolution data for an indicator.
    All datetime objects in the output are in UTC.
    """
    return _get_datetime_data(data, indicator)


def get_hourly_data(
    data: dict[datetime, dict[str, Any]],
    indicator: str,
) -> tuple[list[Any], list[datetime]]:
    """
    Get hourly-resolution data for an indicator.
    All datetime objects in the output are in UTC.
    """
    return _get_datetime_data(data, indicator)


def get_daily_data(
    data: dict[date, dict[str, Any]],
    indicator: str,
) -> tuple[list[Any], list[date]]:
    """
    Get daily-resolution data for an indicator.
    All datetime objects in the output are in UTC.
    """
    indicator_series = []
    time_series = []

    for time_point, values in sorted(data.items()):
        if indicator in values:
            indicator_series.append(values[indicator])
            time_series.append(time_point)

    return indicator_series, time_series


def _parse_timestamp(date_str: str, time_str: str | None, zone_info: tzinfo) -> datetime:
    """Parse a timestamp string into a UTC datetime object."""
    datetime_str = f"{date_str} {time_str}" if time_str else date_str
    local_time = datetime.strptime(datetime_str, "%Y-%m-%d %H:%M:%S").replace(tzinfo=zone_info)
    return local_time.astimezone(UTC)


def _handle_time_aligned_data(
    data: dict[str, Any],
    tags_map: dict[str, str],
    current_time: datetime | date,
    weather_data: dict[Any, dict[str, Any]],
    time_delta: timedelta,
    end_time: datetime | date | None,
) -> None:
    """Handle both current and preceding time-aligned data."""
    current_values = {}
    preceding_values = {}

    for tag, value in data.items():
        if tag in tags_map:
            indicator = tags_map[tag]
            time_alignment = VC_QUERY_ELEMENTS_MAP[indicator].get(
                "time_alignment", TimeAlignment.CURRENT
            )

            if time_alignment == TimeAlignment.PRECEDING:
                preceding_values[indicator] = value
            else:
                current_values[indicator] = value

    if current_values and (end_time is None or current_time <= end_time):
        if current_time not in weather_data:
            weather_data[current_time] = {}
        weather_data[current_time].update(current_values)

    if preceding_values:
        prev_time = current_time - time_delta
        if end_time is None or prev_time <= end_time:
            if prev_time not in weather_data:
                weather_data[prev_time] = {}
            weather_data[prev_time].update(preceding_values)


def parse_weather_data(
    data: dict[str, Any],
    start_time: datetime | None,
    end_time: datetime | None,
) -> ParsedWeather:
    """
    Parse the API response with the requested weather data.
    All datetime objects in the output are in UTC.
    """
    parsed_weather: ParsedWeather = {
        "minute": {},
        "hourly": {},
        "daily": {},
        "location": (data["latitude"], data["longitude"]),
        "timezone": data["timezone"],
        "query_cost": data["queryCost"],
    }

    timezone = data["timezone"]
    zone_info = ZoneInfo(timezone) if timezone != "Z" else UTC

    if end_time is None and start_time is not None:
        end_time = datetime.now(UTC)
    if start_time is None:
        start_time = datetime.now(UTC)

    adjusted_end_time = end_time + timedelta(days=1) if end_time else None

    for daily_data in data["days"]:
        day_date = (
            datetime.strptime(daily_data["datetime"], "%Y-%m-%d").replace(tzinfo=zone_info).date()
        )

        if start_time is not None and day_date < start_time.date():
            continue
        if adjusted_end_time is not None and day_date > adjusted_end_time.date():
            continue

        # handle daily data
        _handle_time_aligned_data(
            daily_data,
            VC_DAILY_TAGS_MAP,
            day_date,
            parsed_weather["daily"],
            timedelta(days=1),
            end_time.date() if end_time else None,
        )

        # handle hourly data
        for hourly_data in daily_data.get("hours", []):
            hourly_time = _parse_timestamp(
                daily_data["datetime"], hourly_data["datetime"], zone_info
            )

            if start_time is not None and hourly_time < start_time:
                continue
            if adjusted_end_time is not None and hourly_time > adjusted_end_time:
                continue

            _handle_time_aligned_data(
                hourly_data,
                VC_HOURLY_TAGS_MAP,
                hourly_time,
                parsed_weather["hourly"],
                timedelta(hours=1),
                end_time,
            )

            # handle minute data
            for minute_data in hourly_data.get("minutes", []):
                minute_time = _parse_timestamp(
                    daily_data["datetime"],
                    f"{hourly_data['datetime']}:{minute_data['datetime']}",
                    zone_info,
                )

                if start_time is not None and minute_time < start_time:
                    continue
                if adjusted_end_time is not None and minute_time > adjusted_end_time:
                    continue

                _handle_time_aligned_data(
                    minute_data,
                    VC_MINUTE_TAGS_MAP,
                    minute_time,
                    parsed_weather["minute"],
                    timedelta(minutes=15),
                    end_time,
                )

    return parsed_weather
