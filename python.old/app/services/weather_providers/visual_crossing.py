# Copyright © 2025 Emissium
# This file is part of the Emissium library.

from __future__ import annotations

import logging
from datetime import UTC, datetime, timedelta
from enum import Enum
from typing import Any

from app.services.weather_providers.api_client import BaseWeatherClient
from app.services.weather_providers.client_requests.message_types import MessageTypes
from app.services.weather_providers.parsers.vc_parser import (
    get_daily_data,
    get_hourly_data,
    get_minute_data,
    parse_weather_data,
)
from app.services.weather_providers.settings.vc_settings import (
    VC_GROUP_INDICATORS,
    VC_QUERY_ELEMENTS_MAP,
    TimeResolution,
    get_indicator_tags,
)
from app.services.weather_providers.types import (
    DailyTimeSeriesData,
    HourlyTimeSeriesData,
    Indicator,
    MinuteTimeSeriesData,
    WeatherResults,
)
from app.services.weather_providers.utils.api_tokens import VCApiKey
from app.services.weather_providers.utils.errors import ApiRequestError, Err


class RequestType(Enum):
    """Type of weather data request."""

    FORECAST = "forecast"
    HISTORICAL = "historical"

    def __str__(self) -> str:
        return self.value


log = logging.getLogger(__name__)
SOURCE = "visualcrossing"
API_NAME = "Visual Crossing API"

# Configuration constants
FORECAST_HOURS = 360
BASE_URL = "https://weather.visualcrossing.com/VisualCrossingWebServices/rest/services/timeline"
MAX_HISTORICAL_DAYS = 365  # maximum days of historical data to fetch

# Map each resolution to its data getter and result type
RESOLUTION_HANDLERS = {
    TimeResolution.MINUTE: (get_minute_data, MinuteTimeSeriesData),
    TimeResolution.HOURLY: (get_hourly_data, HourlyTimeSeriesData),
    TimeResolution.DAILY: (get_daily_data, DailyTimeSeriesData),
}


class VisualCrossingClient(BaseWeatherClient):
    query_tokens: list[VCApiKey]
    _current_token_index: int

    def __init__(self, api_tokens: list[str]) -> None:
        """Initialize the client with the given API tokens."""
        self.query_tokens = []
        self._current_token_index = 0

        for token in api_tokens:
            api_token = VCApiKey(token)
            if api_token.validate():
                self.query_tokens.append(api_token)

        if not self.query_tokens:
            raise ApiRequestError(Err.INVALID_API_KEY, "No valid API tokens provided")

        super().__init__(MessageTypes.request_visualcrossing, log)

    def _get_next_token(self) -> str:
        """Get the next token in rotation."""
        token = self.query_tokens[self._current_token_index].token
        self._current_token_index = (self._current_token_index + 1) % len(self.query_tokens)
        return token

    @staticmethod
    def _validate_coordinates(lat: float, lon: float) -> None:
        """Validate latitude and longitude values."""
        if not (-90 <= lat <= 90):
            raise ApiRequestError(
                Err.INVALID_LOCATION, "Latitude must be between -90 and 90 degrees"
            )
        if not (-180 <= lon <= 180):
            raise ApiRequestError(
                Err.INVALID_LOCATION, "Longitude must be between -180 and 180 degrees"
            )

    @staticmethod
    def _validate_time_range(start: datetime | None, end: datetime | None) -> RequestType:
        """
        Validate time range parameters and determine request type.

        Args:
            start: Start datetime for historical requests
            end: End datetime for historical requests

        Returns:
            RequestType.HISTORICAL for historical requests or RequestType.FORECAST for
            forecast requests

        Raises:
            ApiRequestError: If the time range parameters are invalid
        """
        # Determine request type
        is_historical = end is not None and end < datetime.now(UTC)

        if is_historical:
            # Historical request validation
            if start is None or end is None:
                raise ApiRequestError(
                    Err.INVALID_DATES,
                    "Both start and end dates must be provided for historical requests",
                )

            if end <= start:
                raise ApiRequestError(Err.INVALID_DATES, "End time must be after start time")

            days_diff = (end - start).days
            if days_diff > MAX_HISTORICAL_DAYS:
                raise ApiRequestError(
                    Err.INVALID_DATES,
                    f"Historical time range exceeds maximum of {MAX_HISTORICAL_DAYS} days",
                )

            return RequestType.HISTORICAL
        else:
            # Forecast request validation
            if start is not None or end is not None:
                raise ApiRequestError(
                    Err.INVALID_DATES,
                    "Start and end dates should not be provided for forecast requests",
                )

            return RequestType.FORECAST

    def _collect_requested_indicators(
        self, indicators: list[str] | None, groups: list[str] | None
    ) -> dict[TimeResolution, set[Indicator]]:
        """Collect and categorize requested indicators by their time resolution."""
        # collect all requested indicators first
        requested_indicators = set(indicators or [])

        if groups:
            for group in groups:
                if group in VC_GROUP_INDICATORS:
                    requested_indicators.update(VC_GROUP_INDICATORS[group])

        if not requested_indicators:
            requested_indicators = {
                name for name, setting in VC_QUERY_ELEMENTS_MAP.items() if setting["default"]
            }

        # separate indicators by time resolution
        resolution_indicators: dict[TimeResolution, set[Indicator]] = {
            TimeResolution.MINUTE: set(),
            TimeResolution.HOURLY: set(),
            TimeResolution.DAILY: set(),
        }

        for indicator in requested_indicators:
            if indicator in VC_QUERY_ELEMENTS_MAP:
                resolution = VC_QUERY_ELEMENTS_MAP[indicator].get(
                    "time_resolution",
                    TimeResolution.HOURLY,  # default to hourly if not specified
                )
                resolution_indicators[resolution].add(Indicator(indicator))

        return resolution_indicators

    async def _query_visualcrossing(
        self,
        lat: float,
        lon: float,
        indicators: list[str] | None = None,
        groups: list[str] | None = None,
        request_type: RequestType = RequestType.FORECAST,
        start: datetime | None = None,
        end: datetime | None = None,
    ) -> dict[str, Any] | None:
        """
        Query the Visual Crossing API.
        If no indicators or groups are specified, uses default indicators.

        references:
          - granularity: https://www.visualcrossing.com/resources/documentation/weather-api/sub-hourly-data-in-the-timeline-weather-api/
        """
        # Get the indicator tags to query
        query_elements = get_indicator_tags(indicators, groups)
        if not query_elements:
            raise ApiRequestError(Err.INVALID_INDICATORS, "No valid indicators or groups provided")

        # build URL
        location = f"{lat},{lon}"
        base_url = f"{BASE_URL}/{location}"

        if request_type == RequestType.HISTORICAL:
            if start is None or end is None:
                raise ApiRequestError(
                    Err.INVALID_DATES, "start and end must be provided for historical data"
                )
            base_url = f"{base_url}/{start.strftime('%Y-%m-%d')}/{end.strftime('%Y-%m-%d')}"

        # add query parameters
        query_params = {
            "key": self._get_next_token(),
            "unitGroup": "metric",
            "contentType": "json",
            "timezone": "Z",  # all dates are entered in UTC time
            "elements": ",".join(["datetime", *query_elements]),
            "include": "hours,days,current",  # include all time resolutions
        }

        try:
            return await self.send_request(base_url, params=query_params)
        except ApiRequestError as exc:
            self.log.error(f"Failed to query Visual Crossing API: {exc}")
            return None

    async def _fetch(
        self,
        lat: float,
        lon: float,
        indicators: list[str] | None = None,
        groups: list[str] | None = None,
        start: datetime | None = None,
        end: datetime | None = None,
    ) -> WeatherResults:
        """
        Fetch multiple weather indicators.
        Can specify indicators directly and/or by group name.
        If neither is specified, uses default indicators.

        For historical data, both start and end dates must be provided.
        For forecast data, neither start nor end should be provided.
        """
        # validate input parameters
        self._validate_coordinates(lat, lon)
        request_type = self._validate_time_range(start, end)

        # collect all requested indicators by resolution
        resolution_indicators = self._collect_requested_indicators(indicators, groups)

        # query the weather data
        data = await self._query_visualcrossing(
            lat, lon, indicators, groups, request_type, start, end
        )
        if data is None:
            return _get_empty_weather_results(resolution_indicators)

        # parse the weather data
        weather_data = parse_weather_data(data, start, end)

        # initialize results with proper typing
        results = WeatherResults(minute={}, hourly={}, daily={})

        # process each resolution's data
        for resolution, (data_getter, result_type) in RESOLUTION_HANDLERS.items():
            resolution_key = resolution.value

            # process all indicators for this resolution
            for indicator in resolution_indicators[resolution]:
                values, times = data_getter(weather_data[resolution_key], indicator)
                results[resolution_key][indicator] = result_type(values=values, times=times)

        return results

    async def fetch_historical(
        self,
        lat: float,
        lon: float,
        start: datetime,
        end: datetime,
        indicators: list[str] | None = None,
        groups: list[str] | None = None,
    ) -> WeatherResults:
        """Fetch historical weather data."""
        return await self._fetch(lat, lon, indicators, groups, start=start, end=end)

    async def fetch_forecast(
        self,
        lat: float,
        lon: float,
        indicators: list[str] | None = None,
        groups: list[str] | None = None,
    ) -> WeatherResults:
        """Fetch forecast weather data."""
        return await self._fetch(lat, lon, indicators, groups, start=None, end=None)

    async def fetch_recent(
        self,
        lat: float,
        lon: float,
        indicators: list[str] | None = None,
        groups: list[str] | None = None,
    ) -> WeatherResults:
        """Fetch recent weather data."""
        start = datetime.now(UTC) - timedelta(hours=24)
        end = datetime.now(UTC)
        return await self._fetch(lat, lon, indicators, groups, start=start, end=end)


def _get_empty_weather_results(indicators: dict[TimeResolution, set[Indicator]]) -> WeatherResults:
    """Get an empty weather results object."""
    if not indicators:
        return WeatherResults(minute={}, hourly={}, daily={})

    return WeatherResults(
        minute={
            indicator: MinuteTimeSeriesData(values=[], times=[])
            for indicator in indicators[TimeResolution.MINUTE]
        },
        hourly={
            indicator: HourlyTimeSeriesData(values=[], times=[])
            for indicator in indicators[TimeResolution.HOURLY]
        },
        daily={
            indicator: DailyTimeSeriesData(values=[], times=[])
            for indicator in indicators[TimeResolution.DAILY]
        },
    )
