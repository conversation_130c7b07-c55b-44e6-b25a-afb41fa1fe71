# Copyright © 2025 Emissium
# This file is part of the Emissium library.

from __future__ import annotations

import dataclasses

from app.services.weather_providers.client_requests.message_types import MessageTypes


@dataclasses.dataclass(frozen=True)
class RLSettings:
    frequency: int  # Max request per time period (ie 1 min)
    max_size: int | None = None  # Max size of each request
    max_total_size: int | None = None  # Max cumulative size of all requests in that period


# current rate limits for the messages to API
rate_limits = {
    "default_settings": RLSettings(100),
    "rate_limits_other": {
        MessageTypes.request_visualcrossing: RLSettings(1000),  # 86400/day
    },
}
