# Copyright © 2025 Emissium
# This file is part of the Emissium library.

from __future__ import annotations

import logging
import time
from collections import Counter

from app.services.weather_providers.client_requests.message_types import MessageTypes
from app.services.weather_providers.client_requests.rate_limit_numbers import (
    RLSettings,
    rate_limits,
)

log = logging.getLogger(__name__)


class RateLimiter:
    reset_seconds: int
    current_minute: int
    message_counts: Counter
    percentage_of_limit: int

    def __init__(self, reset_seconds: int = 60, percentage_of_limit: int = 100) -> None:
        """
        Outgoing messages: the counters are only incremented if they are allowed to be sent
        by the rate limiter, since we won't send the messages otherwise.
        """
        self.reset_seconds = reset_seconds
        self.current_minute = int(time.time() // reset_seconds)
        self.message_counts = Counter()
        self.percentage_of_limit = percentage_of_limit

    def process_msg_and_check(self, message_type: MessageTypes) -> bool:
        """
        Returns True if message can be processed successfully, false if a rate limit is passed.
        """
        current_minute = int(time.time() // self.reset_seconds)
        if current_minute != self.current_minute:
            self.current_minute = current_minute
            self.message_counts = Counter()

        new_message_counts: int = self.message_counts[message_type] + 1
        proportion_of_limit: float = self.percentage_of_limit / 100

        ret: bool = False

        try:
            limits: RLSettings = rate_limits["default_settings"]
            if message_type in rate_limits["rate_limits_other"]:
                limits = rate_limits["rate_limits_other"][message_type]

            if new_message_counts > limits.frequency * proportion_of_limit:
                return False

            ret = True
            return True
        finally:
            if ret:
                self.message_counts[message_type] = new_message_counts
