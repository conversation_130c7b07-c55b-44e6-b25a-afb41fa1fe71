# Copyright © 2025 Emissium
# This file is part of the Emissium library.

"""
Indicators compatible with the weather API.
reference: https://www.visualcrossing.com/resources/documentation/weather-api/timeline-weather-api/
"""

from collections.abc import Sequence
from enum import Enum

from app.core.variables import GROUP_TO_VARIABLES


class DataAvailability(str, Enum):
    """Types of data availability."""

    HISTORICAL = "historical"  # indicator is available in historical data
    FORECAST = "forecast"  # indicator is available in forecast data


class TimeAlignment(str, Enum):
    """Time alignment options for indicators."""

    CURRENT = "current"  # indicator returned from VC API refers to the current period
    PRECEDING = "preceding"  # indicator returned from VC API refers to the preceeding period


class TimeResolution(str, Enum):
    """Time resolution options for indicators."""

    MINUTE = "minute"  # indicator is available in minute data
    HOURLY = "hourly"  # indicator is available in hourly data
    DAILY = "daily"  # indicator is available in daily data


# all available tags that can be queried from VC API
VC_QUERY_ELEMENTS_MAP = {
    "temperature": {
        "tag": "temp",
        "unit": "°C",
        "default": True,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "temperature_max": {
        "tag": "tempmax",
        "unit": "°C",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.DAILY,
        "type": float,
    },
    "temperature_min": {
        "tag": "tempmin",
        "unit": "°C",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.DAILY,
        "type": float,
    },
    "feels_like": {
        "tag": "feelslike",
        "unit": "°C",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "dew_point": {
        "tag": "dew",
        "unit": "°C",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "humidity": {
        "tag": "humidity",
        "unit": "%",
        "default": True,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "wind_speed": {
        "tag": "windspeed",
        "unit": "km/h",
        "default": True,
        "description": (
            "Surface-level (10m) sustained wind speed measured as the average speed"
            "of the preceeding one to two minutes"
        ),
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "wind_gust": {
        "tag": "windgust",
        "unit": "km/h",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "wind_direction": {
        "tag": "winddir",
        "unit": "degrees",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "precipitation": {
        "tag": "precip",
        "unit": "mm",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "precipitation_probability": {
        "tag": "precipprob",
        "unit": "%",
        "default": True,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.DAILY,
        "type": float,
    },
    "precipitation_cover": {
        "tag": "precipcover",
        "unit": "%",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.DAILY,
        "type": float,
    },
    "precipitation_type": {
        "tag": "preciptype",
        "unit": None,
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": str,
    },
    "snow": {
        "tag": "snow",
        "unit": "cm",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "snow_depth": {
        "tag": "snowdepth",
        "unit": "cm",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "visibility": {
        "tag": "visibility",
        "unit": "km",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "cloud_cover": {
        "tag": "cloudcover",
        "unit": "%",
        "default": True,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
        "emissium_type": float,
    },
    "solar_radiation": {
        "tag": "solarradiation",
        "unit": "W/m²",
        "default": True,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "solar_energy": {
        "tag": "solarenergy",
        "unit": "MJ/m²",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "uv_index": {
        "tag": "uvindex",
        "unit": None,
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "pressure": {
        "tag": "pressure",
        "unit": "mb",  # millibar
        "default": True,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "conditions": {
        "tag": "conditions",
        "unit": None,
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": str,
    },
    "sunrise": {
        "tag": "sunrise",
        "unit": None,
        "default": True,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.DAILY,
        "type": str,  # HH:MM:SS in UTC if request made with "timezone": "Z"
    },
    "sunset": {
        "tag": "sunset",
        "unit": None,
        "default": True,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.DAILY,
        "type": str,  # HH:MM:SS in UTC if request made with "timezone": "Z"
    },
    "wind_speed_50m": {
        "tag": "windspeed50",
        "unit": "km/h",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "wind_direction_50m": {
        "tag": "winddir50",
        "unit": "degrees",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
        "emissium_type": float,
    },
    "wind_speed_80m": {
        "tag": "windspeed80",
        "unit": "km/h",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "wind_direction_80m": {
        "tag": "winddir80",
        "unit": "degrees",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "wind_speed_100m": {
        "tag": "windspeed100",
        "unit": "km/h",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "wind_direction_100m": {
        "tag": "winddir100",
        "unit": "degrees",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.CURRENT,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "direct_normal_radiation": {
        "tag": "dniradiation",
        "unit": "W/m²",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.PRECEDING,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "diffuse_radiation": {
        "tag": "difradiation",
        "unit": "W/m²",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.PRECEDING,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
        "emissium_type": float,
    },
    "global_horizontal_radiation": {
        "tag": "ghiradiation",
        "unit": "W/m²",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.PRECEDING,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "global_tilt_radiation": {
        "tag": "gtiradiation",
        "unit": "W/m²",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.PRECEDING,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
    "sun_elevation": {
        "tag": "sunelevation",
        "unit": "degrees",
        "default": False,
        "available_in": [DataAvailability.HISTORICAL, DataAvailability.FORECAST],
        "time_alignment": TimeAlignment.PRECEDING,
        "time_resolution": TimeResolution.HOURLY,
        "type": float,
    },
}

# Get all daily indicator tags to indicators mapping
VC_DAILY_TAGS_MAP = {
    setting["tag"]: indicator
    for indicator, setting in VC_QUERY_ELEMENTS_MAP.items()
    if setting.get("time_resolution", TimeResolution.HOURLY) == TimeResolution.DAILY
}

# Get all hourly indicator tags to indicators mapping
VC_HOURLY_TAGS_MAP = {
    setting["tag"]: indicator
    for indicator, setting in VC_QUERY_ELEMENTS_MAP.items()
    if setting.get("time_resolution", TimeResolution.HOURLY) == TimeResolution.HOURLY
}

# Get all minute indicator tags to indicators mapping
VC_MINUTE_TAGS_MAP = {
    setting["tag"]: indicator
    for indicator, setting in VC_QUERY_ELEMENTS_MAP.items()
    if setting.get("time_resolution", TimeResolution.HOURLY) == TimeResolution.MINUTE
}

# Get all indicator tags
VC_INDICATOR_TAGS = [setting["tag"] for setting in VC_QUERY_ELEMENTS_MAP.values()]

# Get tags for default queries
VC_DEFAULT_QUERY_ELEMENTS = [
    setting["tag"] for _, setting in VC_QUERY_ELEMENTS_MAP.items() if setting["default"]
]

# Map groups to their indicator tags
VC_GROUP_TAGS = {
    group: [
        VC_QUERY_ELEMENTS_MAP[indicator]["tag"]
        for indicator in GROUP_TO_VARIABLES[group]
        if indicator in VC_QUERY_ELEMENTS_MAP
    ]
    for group in GROUP_TO_VARIABLES
}

# Map groups to their indicator names
VC_GROUP_INDICATORS = {
    group: [
        indicator for indicator in GROUP_TO_VARIABLES[group] if indicator in VC_QUERY_ELEMENTS_MAP
    ]
    for group in GROUP_TO_VARIABLES
}


def get_indicator_tags(
    indicators: Sequence[str] | None = None, groups: Sequence[str] | None = None
) -> list[str]:
    """
    Get indicator tags based on specified indicators and/or groups. If neither is specified,
    returns default indicators. If both are specified, returns the union of the two.

    Args:
        indicators: List of indicator names or tags
        groups: List of weather groups

    Returns:
        List of indicator tags
    """
    if not indicators and not groups:
        return VC_DEFAULT_QUERY_ELEMENTS

    tags = set()

    # add specific indicators
    if indicators:
        for indicator in indicators:
            if indicator in VC_QUERY_ELEMENTS_MAP:
                tags.add(VC_QUERY_ELEMENTS_MAP[indicator]["tag"])
            elif indicator in VC_INDICATOR_TAGS:
                # the indicator is already a tag
                tags.add(indicator)

    # add all indicators from specified groups
    if groups:
        for group in groups:
            if group in VC_GROUP_TAGS:
                tags.update(VC_GROUP_TAGS[group])

    return list(tags)
