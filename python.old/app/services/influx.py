# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import asyncio
import logging
from collections import deque
from datetime import UTC, date, datetime, timedelta
from typing import cast

import pandas as pd
from influxdb_client.client.flux_table import FluxTable
from influxdb_client.client.influxdb_client import Influx<PERSON><PERSON><PERSON>
from influxdb_client.client.write_api import S<PERSON><PERSON><PERSON><PERSON>NO<PERSON>
from pandas import DatetimeIndex

from app.config import settings
from app.core.variables import VARIABLE_METADATA
from app.models.weather import WeatherDataWithResolutionSerialized
from app.services.weather_manager import Indicator, Resolution, WeatherDataWithResolution

logger = logging.getLogger(__name__)


class InfluxService:
    BATCH_SIZE = 5000  # maximum number of points to batch
    FLUSH_INTERVAL = 30  # seconds between forced flushes

    def __init__(self) -> None:
        self.client = InfluxDBClient(
            url=settings.INFLUXDB_URL,
            token=settings.INFLUXDB_TOKEN,
            org=settings.INFLUXDB_ORG,
        )
        self.write_api = self.client.write_api(write_options=SYNCHRONOUS)
        self.query_api = self.client.query_api()

        # initialize buffer and background task
        self._buffer: deque[dict] = deque()
        self._buffer_lock = asyncio.Lock()
        self._flush_task: asyncio.Task | None = None
        self._start_flush_task()

    def _start_flush_task(self) -> None:
        """Start the background task for time-based buffer flushing."""
        if self._flush_task is None or self._flush_task.done():
            self._flush_task = asyncio.create_task(self._periodic_flush())

    async def _periodic_flush(self) -> None:
        """Periodically flush the buffer based on time interval."""
        while True:
            try:
                await asyncio.sleep(self.FLUSH_INTERVAL)
                await self._flush_buffer()
            except Exception as e:
                logger.error(f"Error in periodic flush: {e!s}")

    async def _flush_buffer(self) -> None:
        """Flush the current buffer to InfluxDB."""
        async with self._buffer_lock:
            if not self._buffer:
                return

            try:
                # create DataFrame from buffer
                df = pd.DataFrame(list(self._buffer))
                df.set_index("timestamp", inplace=True)

                # ensure index is timezone-aware and sorted
                index = cast(DatetimeIndex, df.index)
                if not index.tz:
                    df.index = index.tz_localize(UTC)
                df.sort_index(inplace=True)

                # write to InfluxDB
                self.write_api.write(
                    bucket=settings.INFLUXDB_BUCKET,
                    record=df,
                    data_frame_measurement_name="weather",
                    data_frame_tag_columns=["location_id", "indicator", "resolution"],
                )

                # clear buffer after successful write
                self._buffer.clear()
                logger.debug(f"Flushed {len(df)} points to InfluxDB")
            except Exception as e:
                logger.error(f"Error flushing buffer to InfluxDB: {e!s}")

    async def store_weather_data(
        self, location_id: str, weather_data: WeatherDataWithResolution
    ) -> None:
        """Store weather data using batching with size and time-based triggers."""
        points = []
        for resolution, data in weather_data.items():
            timestamps = data["timestamps"]
            indicators = data["indicators"]

            for indicator, values in indicators.items():
                if indicator not in VARIABLE_METADATA:
                    continue

                for timestamp, value in zip(timestamps, values, strict=True):
                    if value is not None:  # skip None values
                        points.append(
                            {
                                "location_id": location_id,
                                "indicator": indicator,
                                "value": value,
                                "resolution": resolution,
                                "timestamp": pd.to_datetime(timestamp),
                            }
                        )

        if not points:
            return

        # add points to buffer
        async with self._buffer_lock:
            self._buffer.extend(points)

            # if buffer size exceeds limit, flush immediately
            if len(self._buffer) >= self.BATCH_SIZE:
                await self._flush_buffer()

    def _process_query_results(
        self, result: list[FluxTable] | None
    ) -> WeatherDataWithResolutionSerialized | None:
        """Process query results into WeatherDataWithResolutionSerialized format."""
        if not result:
            return None

        # Initialize data structures to collect timestamps and values by resolution
        resolution_data: dict[
            Resolution, dict[datetime | date, dict[Indicator, float | str | None]]
        ] = {
            "15min": {},
            "hourly": {},
            "daily": {},
        }

        # Process results
        for table in result:
            for record in table.records:
                indicator = record.get_field()
                if indicator not in VARIABLE_METADATA:
                    continue

                # Get resolution from record, default to hourly
                resolution = cast(Resolution, record.values.get("resolution", "hourly"))

                # Get timestamp and convert to date for daily resolution
                timestamp = record.get_time()
                if resolution == "daily":
                    timestamp = timestamp.date()

                value = record.get_value()

                # ensure value matches the expected type from metadata
                if value is not None:
                    expected_type = VARIABLE_METADATA[indicator].get("type")
                    if expected_type in (float, str):
                        try:
                            value = expected_type(value)
                        except (ValueError, TypeError):
                            continue  # skip invalid values

                # Initialize timestamp entry if not exists
                if timestamp not in resolution_data[resolution]:
                    resolution_data[resolution][timestamp] = {}

                # Store the value
                typed_indicator = Indicator(indicator)
                resolution_data[resolution][timestamp][typed_indicator] = value

        # Convert collected data to the required format
        weather_data: WeatherDataWithResolutionSerialized = {}

        for resolution, timestamp_data in resolution_data.items():
            if not timestamp_data:
                continue

            # Sort timestamps
            sorted_timestamps = sorted(timestamp_data.keys())

            # Get all unique indicators
            all_indicators = {
                indicator
                for timestamp_values in timestamp_data.values()
                for indicator in timestamp_values
            }

            # Initialize resolution entry
            weather_data[resolution] = {
                "timestamps": [ts.isoformat() for ts in sorted_timestamps],
                "indicators": {},
            }

            # Fill in values for each indicator
            for indicator in all_indicators:
                values = [timestamp_data[ts].get(indicator) for ts in sorted_timestamps]
                weather_data[resolution]["indicators"][indicator] = values

        return weather_data if weather_data else None

    async def get_recent_weather(
        self, location_id: str
    ) -> WeatherDataWithResolutionSerialized | None:
        """Get the last 24 hours of weather data for a location."""
        query = f'''
            from(bucket: "{settings.INFLUXDB_BUCKET}")
                |> range(start: -24h)
                |> filter(fn: (r) => r["location_id"] == "{location_id}")
                |> filter(fn: (r) => r["_measurement"] == "weather")
                |> pivot(rowKey: ["_time"], columnKey: ["_field"], valueColumn: "_value")
        '''
        result = self.query_api.query(query)
        return self._process_query_results(result)

    async def get_historical_weather(
        self, location_id: str, start_time: datetime, end_time: datetime
    ) -> WeatherDataWithResolutionSerialized | None:
        """Get historical weather data for a location between start and end times."""
        query = f'''
            from(bucket: "{settings.INFLUXDB_BUCKET}")
                |> range(start: {start_time.isoformat()}, stop: {end_time.isoformat()})
                |> filter(fn: (r) => r["location_id"] == "{location_id}")
                |> filter(fn: (r) => r["_measurement"] == "weather")
                |> pivot(rowKey: ["_time"], columnKey: ["_field"], valueColumn: "_value")
        '''
        result = self.query_api.query(query)
        return self._process_query_results(result)

    async def get_forecast_weather(
        self, location_id: str
    ) -> WeatherDataWithResolutionSerialized | None:
        """Get weather forecast for the next 14 days."""
        now = datetime.now(UTC)
        end_time = now + timedelta(days=14)

        query = f'''
            from(bucket: "{settings.INFLUXDB_BUCKET}")
                |> range(start: {now.isoformat()}, stop: {end_time.isoformat()})
                |> filter(fn: (r) => r["location_id"] == "{location_id}")
                |> filter(fn: (r) => r["_measurement"] == "weather")
                |> pivot(rowKey: ["_time"], columnKey: ["_field"], valueColumn: "_value")
        '''
        result = self.query_api.query(query)
        return self._process_query_results(result)

    async def cleanup_inactive_locations(self) -> None:
        """Clean up all data for locations that have been inactive.

        A location is considered inactive if it has no data more recent than
        INACTIVE_CLEANUP_DAYS. For such locations, ALL historical data is deleted.
        """
        try:
            cutoff_time = datetime.now(UTC) - timedelta(days=settings.INACTIVE_CLEANUP_DAYS)

            # Find all locations and their latest timestamp in one efficient query
            locations_query = f'''
                from(bucket: "{settings.INFLUXDB_BUCKET}")
                    |> range(start: 0)
                    |> filter(fn: (r) => r["_measurement"] == "weather")
                    |> keep(columns: ["_time", "location_id"])
                    |> group(columns: ["location_id"])
                    |> last(column: "_time")
            '''
            results = self.query_api.query(locations_query)

            # Process results to find inactive locations
            active_locations = set()
            inactive_locations = set()

            for table in results:
                for record in table.records:
                    location_id = record.values["location_id"]
                    last_timestamp = record.get_time()

                    if last_timestamp >= cutoff_time:
                        active_locations.add(location_id)
                    else:
                        inactive_locations.add(location_id)

            if not inactive_locations:
                logger.info("No inactive locations found")
                return

            # Delete all data for inactive locations in batches
            batch_size = 100  # adjust based on your system's capabilities
            inactive_list = sorted(inactive_locations)

            for i in range(0, len(inactive_list), batch_size):
                batch = inactive_list[i : i + batch_size]
                location_predicates = [f'location_id == "{loc}"' for loc in batch]
                predicate = f'_measurement == "weather" and ({" or ".join(location_predicates)})'

                self.client.delete_api().delete(
                    start=datetime.min.replace(tzinfo=UTC),
                    stop=datetime.max.replace(tzinfo=UTC),
                    predicate=predicate,
                    bucket=settings.INFLUXDB_BUCKET,
                    org=settings.INFLUXDB_ORG,
                )

                logger.debug(f"Cleaned up batch of {len(batch)} locations")

            logger.info(f"Cleaned up all data for {len(inactive_locations)} inactive locations")
            logger.debug(f"Inactive locations: {', '.join(sorted(inactive_locations))}")
        except Exception as e:
            logger.error(f"Error cleaning up inactive locations: {e!s}")


influx_service = InfluxService()
