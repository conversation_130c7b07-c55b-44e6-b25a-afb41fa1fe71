# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import json
from datetime import date, datetime
from typing import Any, cast

import redis

from app.config import settings
from app.models.weather import WeatherDataWithResolutionSerialized
from app.services.weather_manager import WeatherDataWithResolution


class RedisService:
    def __init__(self) -> None:
        self.redis = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.REDIS_DB,
            decode_responses=True,
        )

    def _get_location_key(self, location_id: str) -> str:
        return f"location:{location_id}"

    def _get_recent_key(self, location_id: str) -> str:
        return f"recent:{location_id}"

    def _get_forecast_key(self, location_id: str) -> str:
        return f"forecast:{location_id}"

    async def add_active_location(self, location_id: str, location_data: dict[str, Any]) -> None:
        # store location data and set TTL
        key = self._get_location_key(location_id)
        self.redis.set(key, json.dumps(location_data), ex=settings.LOCATION_TTL)

    async def get_location(self, location_id: str) -> dict[str, Any] | None:
        key = self._get_location_key(location_id)
        data = cast(str | None, self.redis.get(key))
        return json.loads(data) if data else None

    async def get_active_locations(self) -> list[str]:
        # get all active location keys and extract IDs
        keys = cast(list[str], self.redis.keys("location:*"))
        return [key.split(":")[1] for key in keys]

    async def deactivate_location(self, location_id: str) -> None:
        # remove location from active set
        key = self._get_location_key(location_id)
        self.redis.delete(key)

    def _json_serializer(self, obj: object) -> str | None:
        """Convert objects to JSON-serializable format."""
        if isinstance(obj, datetime | date):
            return obj.isoformat()
        raise TypeError(f"Type {type(obj)} not serializable")

    async def cache_recent(self, location_id: str, weather_data: WeatherDataWithResolution) -> None:
        # cache recent weather data
        key = self._get_recent_key(location_id)
        self.redis.set(
            key, json.dumps(weather_data, default=self._json_serializer), ex=settings.LOCATION_TTL
        )

        # refresh the location's TTL since we're actively fetching weather for it
        await self.refresh_location_ttl(location_id)

    async def get_cached_recent(
        self, location_id: str
    ) -> WeatherDataWithResolutionSerialized | None:
        key = self._get_recent_key(location_id)
        data = cast(str | None, self.redis.get(key))
        return json.loads(data) if data else None

    async def cache_forecast(
        self, location_id: str, forecast_data: WeatherDataWithResolution
    ) -> None:
        # cache forecast data
        key = self._get_forecast_key(location_id)
        self.redis.set(
            key, json.dumps(forecast_data, default=self._json_serializer), ex=settings.LOCATION_TTL
        )

        # refresh the location's TTL since we're actively fetching forecast for it
        await self.refresh_location_ttl(location_id)

    async def get_cached_forecast(
        self, location_id: str
    ) -> WeatherDataWithResolutionSerialized | None:
        key = self._get_forecast_key(location_id)
        data = cast(str | None, self.redis.get(key))
        return json.loads(data) if data else None

    async def refresh_location_ttl(self, location_id: str) -> None:
        # refresh TTL for active location
        key = self._get_location_key(location_id)
        self.redis.expire(key, settings.LOCATION_TTL)


redis_service = RedisService()
