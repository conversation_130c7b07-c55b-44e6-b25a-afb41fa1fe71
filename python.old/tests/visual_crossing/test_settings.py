# Copyright © 2025 Emissium
# This file is part of the Emissium library.

"""
Tests for weather variable definitions and consistency in Visual Crossing API.
"""

from app.core.variables import VARIABLE_METADATA
from app.services.weather_providers.settings.vc_settings import (
    VC_QUERY_ELEMENTS_MAP,
    DataAvailability,
    TimeAlignment,
    TimeResolution,
)


def test_variable_keys_match() -> None:
    """Test that VC_QUERY_ELEMENTS_MAP variables are a subset of VARIABLE_METADATA."""
    variable_keys = set(VARIABLE_METADATA.keys())
    vc_keys = set(VC_QUERY_ELEMENTS_MAP.keys())

    # Check if all VC keys exist in VARIABLE_METADATA
    missing_in_metadata = vc_keys - variable_keys
    assert not missing_in_metadata, (
        f"VC variables not found in VARIABLE_METADATA: {missing_in_metadata}"
    )

    # Log if there are variables in VARIABLE_METADATA not mapped to VC
    unmapped_variables = variable_keys - vc_keys
    if unmapped_variables:
        print(f"Note: Variables in VARIABLE_METADATA not mapped to VC: {unmapped_variables}")


def test_type_consistency() -> None:
    """Test that type definitions are consistent between both dictionaries."""
    for var_name in VARIABLE_METADATA:
        var_type = VARIABLE_METADATA[var_name]["type"]
        vc_type = VC_QUERY_ELEMENTS_MAP[var_name]["type"]
        assert var_type == vc_type, (
            f"Type mismatch for {var_name}: "
            f"VARIABLE_METADATA: {var_type}, VC_QUERY_ELEMENTS_MAP: {vc_type}"
        )


def test_vc_settings_completeness() -> None:
    """Test that VC settings contain all required fields for each variable."""
    required_fields = {
        "tag",
        "unit",
        "default",
        "available_in",
        "time_alignment",
        "time_resolution",
        "type",
    }

    for var_name, settings in VC_QUERY_ELEMENTS_MAP.items():
        missing_fields = required_fields - set(settings.keys())
        assert not missing_fields, f"Missing fields for {var_name}: {missing_fields}"


def test_vc_settings_field_types() -> None:
    """Test that VC settings fields have correct types."""
    for var_name, settings in VC_QUERY_ELEMENTS_MAP.items():
        # Test tag
        assert isinstance(settings["tag"], str), f"Invalid tag type for {var_name}"

        # Test unit
        assert isinstance(settings["unit"], str | None), f"Invalid unit type for {var_name}"

        # Test default
        assert isinstance(settings["default"], bool), f"Invalid default type for {var_name}"

        # Test available_in
        assert isinstance(settings["available_in"], list), (
            f"Invalid available_in type for {var_name}"
        )
        for availability in settings["available_in"]:
            assert isinstance(availability, DataAvailability), (
                f"Invalid availability type in {var_name}: {type(availability)}"
            )

        # Test time_alignment
        assert isinstance(settings["time_alignment"], TimeAlignment), (
            f"Invalid time_alignment type for {var_name}"
        )

        # Test time_resolution
        assert isinstance(settings["time_resolution"], TimeResolution), (
            f"Invalid time_resolution type for {var_name}"
        )


def test_no_duplicate_vc_tags() -> None:
    """Test that there are no duplicate tags in VC settings."""
    tags = [settings["tag"] for settings in VC_QUERY_ELEMENTS_MAP.values()]
    assert len(tags) == len(set(tags)), "Duplicate tags found in VC_QUERY_ELEMENTS_MAP"
