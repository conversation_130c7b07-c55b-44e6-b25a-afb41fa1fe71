# Copyright © 2025 Emissium
# This file is part of the Emissium library.

"""
Tests for weather variable definitions and consistency in Visual Crossing API.
"""

import pytest

from app.core.variables import VARIABLE_METADATA, WeatherGroup


def test_valid_weather_groups() -> None:
    """Test that all weather groups in VARIABLE_METADATA are valid WeatherGroup enums."""
    for var_name, metadata in VARIABLE_METADATA.items():
        groups = metadata.get("groups")
        if groups:
            assert isinstance(groups, set), f"Groups for {var_name} should be a set"
            for group in groups:
                assert isinstance(group, WeatherGroup), (
                    f"Invalid group type for {var_name}: {type(group)}"
                )


@pytest.mark.parametrize("var_name", VARIABLE_METADATA.keys())
def test_variable_descriptions(var_name: str) -> None:
    """Test that all variables have non-empty descriptions."""
    description = VARIABLE_METADATA[var_name]["description"]
    assert description is not None, f"Missing description for {var_name}"
    assert isinstance(description, str), f"Description for {var_name} must be a string"
