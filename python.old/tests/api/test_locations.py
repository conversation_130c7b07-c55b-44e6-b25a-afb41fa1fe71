# Copyright © 2025 Emissium
# This file is part of the Emissium library.

from fastapi.testclient import TestClient


def test_create_location(client: TestClient) -> None:
    response = client.post(
        "/api/v1/locations",
        json={
            "latitude": 48.8566,
            "longitude": 2.3522,
            "types": ["solar", "consumption"],
        },
    )
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == "48.86:2.35"
    assert data["normalized_lat"] == 48.86
    assert data["normalized_lon"] == 2.35
    assert data["active"] is True


def test_get_location(client: TestClient) -> None:
    # first create a location
    create_response = client.post(
        "/api/v1/locations",
        json={"latitude": 48.8566, "longitude": 2.3522, "types": ["solar"]},
    )
    location_id = create_response.json()["id"]

    # then get it
    response = client.get(f"/api/v1/locations/{location_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == location_id
    assert data["types"] == ["solar"]


def test_list_locations(client: TestClient) -> None:
    response = client.get("/api/v1/locations")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)


def test_delete_location(client: TestClient) -> None:
    # first create a location
    create_response = client.post(
        "/api/v1/locations",
        json={"latitude": 48.8566, "longitude": 2.3522, "types": ["consumption"]},
    )
    location_id = create_response.json()["id"]

    # then delete it
    response = client.delete(f"/api/v1/locations/{location_id}")
    assert response.status_code == 200

    # verify it's gone
    get_response = client.get(f"/api/v1/locations/{location_id}")
    assert get_response.status_code == 404
