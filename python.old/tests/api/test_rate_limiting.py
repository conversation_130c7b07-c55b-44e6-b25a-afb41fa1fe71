# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import asyncio

import pytest
from fastapi.testclient import Test<PERSON>lient


@pytest.mark.asyncio
async def test_api_rate_limiting(client: TestClient):
    """Test API rate limiting behavior."""
    # Make multiple rapid requests
    responses = []
    for _ in range(50):  # adjust number based on your rate limit settings
        response = client.get("/api/v1/weather/test:123/current")
        responses.append(response.status_code)
        await asyncio.sleep(0.01)  # small delay to not overwhelm the server

    # verify rate limiting behavior
    assert 429 in responses, "Rate limiting should trigger 429 status code"

    # verify rate limit headers
    response = client.get("/api/v1/weather/test:123/current")
    assert "X-RateLimit-Limit" in response.headers
    assert "X-RateLimit-Remaining" in response.headers
    assert "X-RateLimit-Reset" in response.headers


def test_invalid_location_id(client: TestClient):
    """Test handling of invalid location IDs."""
    response = client.get("/api/v1/weather/invalid:location/current")
    assert response.status_code == 404
    assert "error" in response.json()


def test_malformed_request_body(client: TestClient):
    """Test handling of malformed request body."""
    response = client.post(
        "/api/v1/locations",
        json={
            "latitude": "invalid",  # should be float
            "longitude": 2.3522,
            "types": ["solar"],
        },
    )
    assert response.status_code == 422  # Unprocessable Entity
    error_detail = response.json()["detail"]
    assert any("latitude" in error["loc"] for error in error_detail)


def test_missing_required_fields(client: TestClient):
    """Test handling of missing required fields."""
    response = client.post(
        "/api/v1/locations",
        json={
            "latitude": 48.8566,
            # missing longitude
            "types": ["solar"],
        },
    )
    assert response.status_code == 422
    error_detail = response.json()["detail"]
    assert any("longitude" in error["loc"] for error in error_detail)


def test_invalid_date_format(client: TestClient):
    """Test handling of invalid date format in historical data request."""
    response = client.get(
        "/api/v1/weather/test:123/history",
        params={"start": "invalid-date", "end": "2025-01-01T00:00:00Z"},
    )
    assert response.status_code == 422
    assert "invalid" in response.json()["detail"][0]["msg"].lower()


@pytest.mark.asyncio
async def test_concurrent_requests(client: TestClient):
    """Test handling of concurrent requests."""

    async def make_request():
        response = client.get("/api/v1/weather/test:123/current")
        return response.status_code

    # Make 10 concurrent requests
    tasks = [make_request() for _ in range(10)]
    results = await asyncio.gather(*tasks)

    # All requests should either succeed or be rate limited
    assert all(status in [200, 429] for status in results)


def test_invalid_api_version(client: TestClient):
    """Test handling of invalid API version."""
    response = client.get("/api/v2/weather/test:123/current")  # v2 doesn't exist
    assert response.status_code == 404
    assert "error" in response.json()


def test_method_not_allowed(client: TestClient):
    """Test handling of incorrect HTTP methods."""
    response = client.post("/api/v1/weather/test:123/current")  # POST not allowed
    assert response.status_code == 405
    assert "error" in response.json()
