# Copyright © 2025 Emissium
# This file is part of the Emissium library.

from datetime import UTC, datetime, timedelta

import pytest
from fastapi.testclient import TestClient


@pytest.fixture
def test_location(client: TestClient) -> dict:
    response = client.post(
        "/api/v1/locations",
        json={
            "latitude": 48.8566,
            "longitude": 2.3522,
            "types": ["solar", "consumption"],
        },
    )
    return response.json()


def test_get_current_weather(client: TestClient, test_location: dict) -> None:
    location_id = test_location["id"]
    response = client.get(f"/api/v1/weather/{location_id}/current")
    assert response.status_code in [200, 404]  # 404 is acceptable if no data yet
    if response.status_code == 200:
        data = response.json()
        assert data["location_id"] == location_id
        assert "temperature" in data
        assert "humidity" in data


def test_get_historical_weather(client: TestClient, test_location: dict) -> None:
    location_id = test_location["id"]
    end = datetime.now(UTC)
    start = end - timedelta(days=1)

    response = client.get(
        f"/api/v1/weather/{location_id}/history",
        params={"start": start.isoformat(), "end": end.isoformat()},
    )
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)


def test_get_weather_forecast(client: TestClient, test_location: dict) -> None:
    location_id = test_location["id"]
    response = client.get(f"/api/v1/weather/{location_id}/forecast")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    if data:  # if we have forecast data
        assert "forecast_time" in data[0]
        assert "temperature" in data[0]


def test_invalid_date_range(client: TestClient) -> None:
    location_id = "test:123"
    end = datetime.now(UTC)
    start = end + timedelta(days=1)  # start after end

    response = client.get(
        f"/api/v1/weather/{location_id}/history",
        params={"start": start.isoformat(), "end": end.isoformat()},
    )
    assert response.status_code == 400  # should fail with bad request
