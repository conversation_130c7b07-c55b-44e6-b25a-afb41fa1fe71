# Copyright © 2025 Emissium
# This file is part of the Emissium library.

import os
from collections.abc import AsyncGenerator, Generator
from datetime import UTC, datetime

import pytest
from fastapi.testclient import TestClient

from app.main import app
from app.services.redis import redis_service


@pytest.fixture(scope="session")
def client() -> Generator:
    # set test environment variables

    # version info
    os.environ["VERSION"] = "0.1.0-test"
    os.environ["BUILD_DATE"] = datetime.now(UTC).isoformat()

    # service settings
    os.environ["COORDINATE_PRECISION"] = "2"
    os.environ["POLLING_INTERVAL"] = "900"
    os.environ["INACTIVE_CLEANUP_DAYS"] = "30"

    # create test client
    with TestClient(app) as test_client:
        yield test_client


@pytest.fixture(autouse=True)
async def cleanup() -> AsyncGenerator[None, None]:
    # setup - clean any existing test data
    yield
    # cleanup after test
    try:
        # clean Redis
        test_keys = await redis_service.redis.keys("test:*")
        if test_keys:
            await redis_service.redis.delete(*test_keys)

        # clean InfluxDB (in a real setup, you'd want to use a test bucket)
        # this is handled by using a separate test bucket defined in the environment variables
        pass

    except Exception as e:
        print(f"Error during cleanup: {e!s}")
