# Golangci-lint configuration for Zephyr Weather Service
# https://golangci-lint.run/usage/configuration/

run:
  timeout: 5m
  issues-exit-code: 1
  tests: true
  modules-download-mode: readonly
  allow-parallel-runners: true

output:
  format: colored-line-number
  print-issued-lines: true
  print-linter-name: true
  uniq-by-line: true
  sort-results: true

linters-settings:
  gofmt:
    simplify: true

  goimports:
    local-prefixes: github.com/emissium/zephyr

  govet:
    enable-all: true
    disable:
      - fieldalignment # Can be noisy for structs

  golint:
    min-confidence: 0.8

  gocyclo:
    min-complexity: 15

  misspell:
    locale: US

  lll:
    line-length: 120

  goconst:
    min-len: 3
    min-occurrences: 3

  funlen:
    lines: 100
    statements: 50

  nestif:
    min-complexity: 5

  gocognit:
    min-complexity: 20

  godox:
    keywords:
      - TODO
      - BUG
      - FIXME
      - HACK

  errcheck:
    check-type-assertions: true
    check-blank: true

  gocritic:
    enabled-tags:
      - diagnostic
      - performance
      - style
      - experimental
    disabled-checks:
      - commentedOutCode
      - whyNoLint

linters:
  enable:
    # Default linters
    - errcheck
    - gosimple
    - govet
    - ineffassign
    - staticcheck
    - typecheck
    - unused

    # Additional linters
    - bodyclose
    - deadcode
    - depguard
    - dogsled
    - dupl
    - exportloopref
    - funlen
    - gochecknoinits
    - goconst
    - gocritic
    - gocyclo
    - gofmt
    - goimports
    - golint
    - goprintffuncname
    - gosec
    - interfacer
    - lll
    - misspell
    - nakedret
    - noctx
    - nolintlint
    - rowserrcheck
    - scopelint
    - structcheck
    - stylecheck
    - unconvert
    - unparam
    - varcheck
    - whitespace
    - gocognit
    - godox
    - nestif
    - prealloc
    - godot
    - gochecknoglobals
    - goerr113
    - wsl
    - gofumpt

  disable:
    - maligned # Deprecated
    - interfacer # Deprecated

issues:
  exclude-rules:
    # Exclude some linters from running on tests files
    - path: _test\.go
      linters:
        - gocyclo
        - errcheck
        - dupl
        - gosec
        - funlen
        - gocognit
        - scopelint
        - lll
        - gochecknoglobals

    # Exclude generated files
    - path: api/gen/
      linters:
        - golint
        - stylecheck
        - gochecknoglobals
        - gochecknoinits
        - lll
        - godot
        - wsl

    # Allow long lines in generated files
    - path: api/gen/
      linters:
        - lll

    # Exclude specific rules for main files
    - path: cmd/
      linters:
        - gochecknoglobals
        - gochecknoinits

    # Allow init functions in main packages
    - path: cmd/.*\.go
      text: "don't use `init` function"

    # Exclude TODO comments from main development
    - path: internal/
      linters:
        - godox
      text: "TODO"

  exclude-use-default: false
  max-issues-per-linter: 0
  max-same-issues: 0
  new: false

severity:
  default-severity: error
  case-sensitive: false
  rules:
    - linters:
        - dupl
      severity: info
    - linters:
        - gocritic
      severity: info
    - linters:
        - godox
      severity: warning
