# 🌤️ Zephyr Weather Service

This service collects weather data from external APIs for thousands of locations across Europe, storing the data locally for near real-time, historical and forecasts analysis. It is designed to be scalable, efficient, and event-driven — only fetching data for active locations.

## 🎯 Features

- High-precision weather data collection from Visual Crossing API
- Location deduplication and normalization
- Efficient data storage with InfluxDB (time-series) and Redis (caching)
- Auto-cleanup of inactive location data
- RESTful API for location and weather data management

## 📍 Location Deduplication Strategy

To avoid redundant polling of nearby locations with nearly identical coordinates, the service implements a coordinate normalization strategy.

When a new location is added or queried, the latitude and longitude are rounded to a fixed precision (e.g., 0.01°, roughly ~1.1 km). This reduces duplication and ensures that multiple nearby locations are grouped under a single identifier.

### 🔐 How Location IDs Are Generated

Each location added to the system is assigned a deterministic ID based on its normalized coordinates using the storage precision.

```go
lat := round(input_lat, 2)
lon := round(input_lon, 2)
location_id := fmt.Sprintf("%f:%f", lat, lon)  // e.g. "48.86:2.35"
```

This ID is:
* Used as the key in Redis to track active locations
* Used to tag time-series data in InfluxDB
* Ensures the same location ID is reused if another request falls into the same rounded coordinate

## 🔄 Data Flow & Key Patterns

### Active Location Registry (Redis)
```redis
location:{id}             # Active location marker with TTL
location:{id}:last_poll   # Timestamp of last successful poll
```
* TTL-based activity tracking (300 minutes)
* Auto-expiration for inactive locations
* Sliding window to prevent stale re-entry

### Time-Series Storage (InfluxDB)
```sql
weather_actual    # Real-time and historical measurements
weather_forecast  # 14-day predictions (15-min resolution)
```
* Location tags: `location_id`, `lat`, `lon`
* 30-day retention policy for inactive locations

### Polling Strategy
* Initial fetch on first activation
* 15-minute polling cycle for active locations
* Concurrent fetching with `asyncio.gather()`
* Dual-write to InfluxDB (persistence) and Redis (cache)

## 🏗️ Architecture

### Overview

Zephyr is designed as a layered, containerized application optimized for reliability and scalability. Each component is isolated in its own container, communicating through well-defined interfaces.

### Component Interaction
```mermaid
graph TD
    A[External Service] -->|HTTPS/443| B[Proxy Layer]
    B -->|HTTP/8000| C[API Layer]
    C --> D[State Layer]
    C --> E[Worker Layer]
    D -->|Redis| F[Active Locations]
    D -->|InfluxDB| G[Time Series Data]
    E -->|Polling| D
    H[Weather API] -->|Fetch weather| E
```

### System Layers

```
[Proxy Layer] - Production Security
   🔹 Traefik (Coolify)
   🔹 TLS/HTTPS termination (Let's Encrypt)
   🔹 Load balancing & rate limiting
   🔹 DDoS protection
   🔹 Forwards to API Layer (8000)
   🔹 Handles SSL/TLS certificate renewal

[API Layer] - container 1 - custom Dockerfile
   🔹 Echo application (gRPC & REST endpoints)
      • Protocol Buffers (Proto) via bufbuild
      • ConnectRPC for REST API & gRPC for internal communication
      • Location registration and management
      • Weather data retrieval (current/historical/forecast)
      • Health monitoring and metrics
      • OpenAPI documentation (Swagger)
   🔹 Input validation & error handling
   🔹 Authentication & rate limiting
   🔹 Interacts with Redis and InfluxDB

[Worker Layer] - container 2 - custom Dockerfile.worker
   🔹 Task Scheduler
      • Coordinates periodic updates
      • Manages cleanup jobs
      • Handles retry logic
   🔹 Weather Fetcher
      • Parallel processing of active locations
      • Intelligent polling based on update frequency
      • Error handling and logging
   🔹 Data Processing
      • Normalization of weather data
      • Physical validation of measurements
      • Unit conversion and calculations
   🔹 External API Integration
      • Visual Crossing API client
      • Rate limit management
      • Connection pooling

[State Layer] - containers 3 & 4 - Data Persistence
   🔹 Redis (redis:7.2-alpine)
      • Active locations registry
      • Short-term weather cache (5 hours TTL)
      • Fast lookups for current conditions
      • Configurable persistence (AOF/RDB)
      • Memory-optimized data structures

   🔹 InfluxDB (influxdb:2.7.10-alpine)
      • Time-series data storage
      • Historical weather records
      • Forecast data (24-hour predictions)
      • System metrics and analytics
      • Data retention policies
      • Automatic data downsampling
```

### Scaling Considerations

- API layer can be scaled horizontally behind Nginx
- Worker layer can be scaled based on active location count
- Redis can be clustered for higher throughput
- InfluxDB supports clustering for larger deployments


## 💾 Persistence and Restart Behavior
| Component | Persists After Reboot? | Notes                                                                 |
|-----------|------------------------|-----------------------------------------------------------------------|
| InfluxDB  | ✅ Yes (persistent volume required) | Stores all historical and forecast data; implements cleanup for inactive location                        |
| Redis     | ❌ No (in-memory only by default)   | Tracks active locations + caches recent results. Can enable persistence with AOF or RDB. |
| Echo      | ✅ (stateless)                      | Stateless service — reads from Redis and InfluxDB                     |
| Poller    | ✅ (stateless)                      | Also stateless — rebuilds list of active locations from Redis at runtime |

## 📊 Data Models

### Location Types & Variables

Each location can be assigned one or more types (e.g. ["solar_prod", "wind_onshore"]), which define the set of weather variables to track for that location and optimized for its use case. This enables shared polling logic across use cases like energy consumption modeling and renewables forecasting.


| Variable | `solar_prod` | `wind_onshore` | `consumption` | Unit & Range |
|----------|------------|------|-------------|--------------|
| Temperature | ✓ | ✓ | ✓ | °C |
| Humidity | - | ✓ | ✓ | % |
| Humidity | - | ✓ | ✓ | % |
| Wind Speed (10m) | - | ✓ | ✓ | km/h |
| Wind Speed (50m, 80m) | - | ✓ | - | km/h |
| Wind Speed (100m) | - | ✓ | - | km/h |
| Wind Direction | - | ✓ | - | degrees |
| Pressure | - | ✓ | - | mb |
| Global Horizontal Radiation | ✓ | - | - | W/m² |
| Direct Normal Radiation | ✓ | - | - | W/m² |
| Global Tilt Radiation | ✓ | - | - | W/m² |
| Cloud Cover | ✓ | - | - | % |
| Solar Radiation | ✓ | - | - | W/m² |
| Feels Like Temperature | - | - | ✓ | °C |
| Dew Point | - | - | ✓ | °C |
| Snow | - | - | ✓ | cm |
| Snow Depth | - | - | ✓ | cm |

## 🚀 Getting Started

### Prerequisites

- Docker and Docker Compose
- Visual Crossing API credentials
- Golang 1.20+ (for building the Echo service)
- Git (for installation from GitHub)

### Configuration

1. Create a `.env` file in the project root:

```env
# Visual Crossing API Settings
VISUAL_CROSSING_API_KEY=your-api-key
VISUAL_CROSSING_BASE_URL=https://weather.visualcrossing.com/VisualCrossingWebServices/rest/services/timeline

# InfluxDB Settings
INFLUXDB_TOKEN=your-super-secret-token
```

### Production Deployment

> [!NOTE]\
> Coming soon


<!-- The API documentation will be available at:
- Swagger UI: http://localhost:8000/api/v1/docs
- ReDoc: http://localhost:8000/api/v1/redoc
- OpenAPI JSON: http://localhost:8000/api/v1/openapi.json -->

## 📡 API Endpoints

### Location Management

- `POST /api/v1/locations`: Add or activate a location
- `GET /api/v1/locations/{location_id}`: Get location details
- `GET /api/v1/locations`: List all active locations
- `DELETE /api/v1/locations/{location_id}`: Deactivate a location

### Weather Data

- `GET /api/v1/weather/{location_id}/current`: Get current weather
- `GET /api/v1/weather/{location_id}/history`: Get historical weather data
- `GET /api/v1/weather/{location_id}/forecast`: Get weather forecast (24-hour hourly forecast)

### System

- `GET /api/v1/health`: Service health check


## 🔧 Development

### Local Development Setup

1. Clone the repository:
```bash
git clone https://github.com/emissium/zephyr.git
cd zephyr
```

> [!NOTE]\
> Coming soon

### Running Tests

> [!NOTE]\
> Coming soon

### Project Structure

```
zephyr/
├── app/                         # All application code lives here
│   ├── api/                     # Echo routers
│   │   └── endpoints/           # REST API endpoints grouped by feature
│   ├── core/                    # Business logic
│   │   ├── registry.go          # Location registry
│   │   ├── variables.go         # Weather variable definitions
│   │   └── normalization.go     # Data normalization
│   ├── models/                  # Pydantic schemas and enums
│   ├── services/                # External services
│   │   ├── weather_manager.go   # Visual Crossing API client
│   │   ├── influx.go            # InfluxDB client
│   │   └── redis.go             # Redis client
│   └── workers/                 # Background worker code (pollers, schedulers)
│       ├── scheduler.go         # Task scheduler
│       └── fetch_weather.go     # Weather fetcher
├── scripts/                     # Utility scripts, CLI tools, migrations, etc.
├── docker-compose.yml           # Docker services configuration (full stack)
├── Dockerfile                   # Echo service container
├── Dockerfile.worker            # Worker service container
├── LICENSE                      # Proprietary license
```

## 📝 License

Zephyr is proprietary software. Unauthorized copying of this file, via any medium, is strictly prohibited. For more details, see the [LICENSE](LICENSE) file.
