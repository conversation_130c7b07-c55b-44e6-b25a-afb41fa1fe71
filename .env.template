# Version Info
VERSION=0.1.0
BUILD_DATE=
GIT_COMMIT=

# API Settings
API_V1_STR=/api/v1
PROJECT_NAME=Zephyr Weather Service
API_TOKEN=your-super-secret-token  # Change this!

# Documentation Settings
DOCS_USERNAME=admin
DOCS_PASSWORD=admin  # Change this!

# Redis Settings
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
LOCATION_TTL=18000  # 5 hours in seconds

# InfluxDB Settings
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your-super-secret-token  # Change this!
INFLUXDB_ORG=emissium
INFLUXDB_BUCKET=weather_data

# Visual Crossing API Settings
VISUAL_CROSSING_API_KEY=your-api-key
VISUAL_CROSSING_BASE_URL=https://weather.visualcrossing.com/VisualCrossingWebServices/rest/services/timeline

# Service Settings
COORDINATE_PRECISION=2
POLLING_INTERVAL=900  # 15 minutes in seconds
INACTIVE_CLEANUP_DAYS=30
