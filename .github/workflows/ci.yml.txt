name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  GO_VERSION: '1.21'
  BUF_VERSION: 'latest'

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      influxdb:
        image: influxdb:2.7-alpine
        ports:
          - 8086:8086
        env:
          DOCKER_INFLUXDB_INIT_MODE: setup
          DOCKER_INFLUXDB_INIT_USERNAME: admin
          DOCKER_INFLUXDB_INIT_PASSWORD: password123
          DOCKER_INFLUXDB_INIT_ORG: zephyr
          DOCKER_INFLUXDB_INIT_BUCKET: weather
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
    
    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-
    
    - name: Install tools
      run: make install-tools
    
    - name: Check dependencies
      run: make check-deps
    
    - name: Download dependencies
      run: make deps
    
    - name: Generate code
      run: make generate
    
    - name: Run linting
      run: make lint
    
    - name: Run security checks
      run: make security
    
    - name: Run tests
      run: make test-verbose
    
    - name: Upload coverage to Codecov
      if: success()
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.out
        flags: unittests
        name: codecov-umbrella
    
    - name: Run integration tests
      run: make test-integration
      env:
        REDIS_URL: redis://localhost:6379
        INFLUX_URL: http://localhost:8086

  build:
    name: Build
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
    
    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-
    
    - name: Install tools
      run: make install-tools
    
    - name: Download dependencies
      run: make deps
    
    - name: Generate code
      run: make generate
    
    - name: Build all targets
      run: make build-all
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: binaries
        path: build/
        retention-days: 30

  docker:
    name: Docker Build
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' || github.event_name == 'release'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      if: github.event_name == 'release'
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ghcr.io/${{ github.repository }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: ${{ github.event_name == 'release' }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  release:
    name: Release
    runs-on: ubuntu-latest
    needs: [test, build]
    if: github.event_name == 'release'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}
    
    - name: Install tools
      run: make install-tools
    
    - name: Download dependencies
      run: make deps
    
    - name: Generate code
      run: make generate
    
    - name: Cross-compile
      run: make cross-compile
    
    - name: Create release assets
      run: |
        cd build
        for binary in zephyr-*; do
          if [[ "$binary" == *.exe ]]; then
            zip "${binary}.zip" "$binary"
          else
            tar -czf "${binary}.tar.gz" "$binary"
          fi
        done
    
    - name: Upload release assets
      uses: softprops/action-gh-release@v1
      with:
        files: |
          build/*.tar.gz
          build/*.zip
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
