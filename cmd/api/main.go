package main

import (
	"fmt"
	"log"
	"net/http"

	"github.com/emissium/zephyr/api/gen/location/v1/locationv1connect"
	locationapp "github.com/emissium/zephyr/internal/application/location"
	"golang.org/x/net/http2"
	"golang.org/x/net/http2/h2c"
)

func main() {
	// Create location service with default coordinate precision
	locationService := locationapp.NewService(6) // 6 decimal places precision

	// Create ConnectRPC handlers
	mux := http.NewServeMux()
	path, handler := locationv1connect.NewLocationServiceHandler(locationService)
	mux.Handle(path, handler)

	// Add health check endpoint
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		fmt.Fprint(w, `{"status":"healthy","service":"zephyr-api"}`)
	})

	// Create HTTP/2 server with h2c (HTTP/2 cleartext) support for development
	server := &http.Server{
		Addr:    ":8080",
		Handler: h2c.NewHandler(mux, &http2.Server{}),
	}

	fmt.Println("🌤️  Zephyr Weather Service starting on :8080")
	fmt.Println("📍 Location Service: " + path)
	fmt.Println("🏥 Health Check: /health")
	fmt.Println("🔗 gRPC-Web and REST endpoints available")

	if err := server.ListenAndServe(); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
