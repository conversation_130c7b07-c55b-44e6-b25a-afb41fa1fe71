package location

import (
	"fmt"
	"math"

	errbuilder "github.com/ZanzyTHEbar/errbuilder-go"
	locationv1 "github.com/emissium/zephyr/api/gen/location/v1"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// Normalizer handles coordinate normalization for location management
type Normalizer struct {
	precision int // Number of decimal places for coordinate rounding
}

// NewNormalizer creates a new location normalizer with the specified precision
func NewNormalizer(precision int) *Normalizer {
	return &Normalizer{
		precision: precision,
	}
}

// NormalizeCoordinate rounds a coordinate to the specified precision
func (n *Normalizer) NormalizeCoordinate(coord float64) float64 {
	multiplier := math.Pow(10, float64(n.precision))
	return math.Round(coord*multiplier) / multiplier
}

// GenerateLocationID creates a unique location identifier from coordinates
func (n *Normalizer) GenerateLocationID(lat, lon float64) string {
	normLat := n.NormalizeCoordinate(lat)
	normLon := n.NormalizeCoordinate(lon)
	return fmt.Sprintf("%g:%g", normLat, normLon)
}

// ValidateCoordinates ensures latitude and longitude are within valid ranges
func (n *Normalizer) ValidateCoordinates(lat, lon float64) error {
	if lat < -90 || lat > 90 {
		return errbuilder.NewErrBuilder().
			WithLabel("validation_error").
			WithMsg("latitude must be between -90 and 90").
			WithDetails(errbuilder.NewErrDetails(errbuilder.ErrorMap{"latitude": fmt.Errorf("%f", lat)}))
	}

	if lon < -180 || lon > 180 {
		return errbuilder.NewErrBuilder().
			WithLabel("validation_error").
			WithMsg("longitude must be between -180 and 180").
			WithDetails(errbuilder.NewErrDetails(errbuilder.ErrorMap{"longitude": fmt.Errorf("%f", lon)}))
	}

	return nil
}

// CreateLocation creates a new location from a request, performing normalization
func (n *Normalizer) CreateLocation(req *locationv1.CreateLocationRequest) (*locationv1.Location, error) {
	// Validate coordinates
	if err := n.ValidateCoordinates(req.Latitude, req.Longitude); err != nil {
		return nil, err
	}

	// Validate types
	if len(req.Types) == 0 {
		return nil, errbuilder.NewErrBuilder().
			WithLabel("validation_error").
			WithMsg("location types cannot be empty")
	}

	// Normalize coordinates
	normalizedLat := n.NormalizeCoordinate(req.Latitude)
	normalizedLon := n.NormalizeCoordinate(req.Longitude)

	// Generate location ID
	locationID := n.GenerateLocationID(req.Latitude, req.Longitude)

	// Create location using generated protobuf type
	location := &locationv1.Location{
		Id:            locationID,
		Latitude:      req.Latitude,
		Longitude:     req.Longitude,
		NormalizedLat: normalizedLat,
		NormalizedLon: normalizedLon,
		Types:         req.Types,
		CreatedAt:     timestamppb.Now(),
		Active:        true,
	}

	return location, nil
}
