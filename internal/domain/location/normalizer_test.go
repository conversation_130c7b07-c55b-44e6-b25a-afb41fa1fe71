package location

import (
	"testing"

	locationv1 "github.com/emissium/zephyr/api/gen/location/v1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNormalizer_NormalizeCoordinate(t *testing.T) {
	tests := []struct {
		name      string
		precision int
		input     float64
		expected  float64
	}{
		{
			name:      "normalize to 2 decimal places",
			precision: 2,
			input:     12.3456789,
			expected:  12.35,
		},
		{
			name:      "normalize to 6 decimal places",
			precision: 6,
			input:     12.3456789,
			expected:  12.345679,
		},
		{
			name:      "negative coordinate",
			precision: 3,
			input:     -45.6789123,
			expected:  -45.679,
		},
		{
			name:      "zero coordinate",
			precision: 4,
			input:     0.0,
			expected:  0.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			normalizer := NewNormalizer(tt.precision)
			result := normalizer.NormalizeCoordinate(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestNormalizer_GenerateLocationID(t *testing.T) {
	tests := []struct {
		name      string
		precision int
		lat       float64
		lon       float64
		expected  string
	}{
		{
			name:      "standard coordinates",
			precision: 6,
			lat:       40.7128,
			lon:       -74.0060,
			expected:  "40.7128:-74.006",
		},
		{
			name:      "coordinates requiring normalization",
			precision: 2,
			lat:       40.712856,
			lon:       -74.005973,
			expected:  "40.71:-74.01",
		},
		{
			name:      "zero coordinates",
			precision: 4,
			lat:       0.0,
			lon:       0.0,
			expected:  "0:0",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			normalizer := NewNormalizer(tt.precision)
			result := normalizer.GenerateLocationID(tt.lat, tt.lon)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestNormalizer_ValidateCoordinates(t *testing.T) {
	tests := []struct {
		name      string
		lat       float64
		lon       float64
		expectErr bool
		errLabel  string
	}{
		{
			name:      "valid coordinates",
			lat:       40.7128,
			lon:       -74.0060,
			expectErr: false,
		},
		{
			name:      "invalid latitude too high",
			lat:       91.0,
			lon:       -74.0060,
			expectErr: true,
			errLabel:  "validation_error",
		},
		{
			name:      "invalid latitude too low",
			lat:       -91.0,
			lon:       -74.0060,
			expectErr: true,
			errLabel:  "validation_error",
		},
		{
			name:      "invalid longitude too high",
			lat:       40.7128,
			lon:       181.0,
			expectErr: true,
			errLabel:  "validation_error",
		},
		{
			name:      "invalid longitude too low",
			lat:       40.7128,
			lon:       -181.0,
			expectErr: true,
			errLabel:  "validation_error",
		},
		{
			name:      "boundary values valid",
			lat:       90.0,
			lon:       180.0,
			expectErr: false,
		},
		{
			name:      "boundary values valid negative",
			lat:       -90.0,
			lon:       -180.0,
			expectErr: false,
		},
	}

	normalizer := NewNormalizer(6)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := normalizer.ValidateCoordinates(tt.lat, tt.lon)
			if tt.expectErr {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errLabel)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestNormalizer_CreateLocation(t *testing.T) {
	tests := []struct {
		name      string
		precision int
		request   *locationv1.CreateLocationRequest
		expectErr bool
		errLabel  string
	}{
		{
			name:      "valid location creation",
			precision: 6,
			request: &locationv1.CreateLocationRequest{
				Latitude:  40.7128,
				Longitude: -74.0060,
				Types:     []string{"consumption", "solar"},
			},
			expectErr: false,
		},
		{
			name:      "invalid coordinates",
			precision: 6,
			request: &locationv1.CreateLocationRequest{
				Latitude:  91.0,
				Longitude: -74.0060,
				Types:     []string{"consumption"},
			},
			expectErr: true,
			errLabel:  "validation_error",
		},
		{
			name:      "empty types",
			precision: 6,
			request: &locationv1.CreateLocationRequest{
				Latitude:  40.7128,
				Longitude: -74.0060,
				Types:     []string{},
			},
			expectErr: true,
			errLabel:  "validation_error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			normalizer := NewNormalizer(tt.precision)
			location, err := normalizer.CreateLocation(tt.request)

			if tt.expectErr {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errLabel)
				assert.Nil(t, location)
			} else {
				require.NoError(t, err)
				require.NotNil(t, location)

				// Verify location properties
				assert.Equal(t, tt.request.Latitude, location.Latitude)
				assert.Equal(t, tt.request.Longitude, location.Longitude)
				assert.Equal(t, tt.request.Types, location.Types)
				assert.True(t, location.Active)
				assert.NotEmpty(t, location.Id)
				assert.NotNil(t, location.CreatedAt)

				// Verify normalization
				expectedNormLat := normalizer.NormalizeCoordinate(tt.request.Latitude)
				expectedNormLon := normalizer.NormalizeCoordinate(tt.request.Longitude)
				assert.Equal(t, expectedNormLat, location.NormalizedLat)
				assert.Equal(t, expectedNormLon, location.NormalizedLon)

				// Verify ID format
				expectedID := normalizer.GenerateLocationID(tt.request.Latitude, tt.request.Longitude)
				assert.Equal(t, expectedID, location.Id)
			}
		})
	}
}
