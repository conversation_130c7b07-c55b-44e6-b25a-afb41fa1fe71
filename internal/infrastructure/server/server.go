package server

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"
	"os/signal"
	"syscall"
	"time"

	connectcors "connectrpc.com/cors"
	"github.com/emissium/zephyr/internal/infrastructure/config"
	"github.com/emissium/zephyr/internal/infrastructure/server/middleware"
	"github.com/rs/cors"
	"golang.org/x/net/http2"
	"golang.org/x/net/http2/h2c"
)

// TODO: ConnectRPC supports gRPC & REST endpoints - add support for both handlers

// Server represents the main HTTP server with dependency injection
type Server struct {
	config     *config.Config
	mux        *http.ServeMux
	middleware []middleware.Middleware
	routes     []RouteGroup
}

// RouteGroup represents a collection of related routes
type RouteGroup struct {
	Prefix  string
	Handler http.Handler
}

// NewServer creates a new server instance with dependency injection
func NewServer(cfg *config.Config) *Server {
	return &Server{
		config:     cfg,
		mux:        http.NewServeMux(),
		middleware: make([]middleware.Middleware, 0),
		routes:     make([]RouteGroup, 0),
	}
}

// AddMiddleware adds middleware to the server middleware stack
func (s *Server) AddMiddleware(mw middleware.Middleware) {
	s.middleware = append(s.middleware, mw)
}

// AddRouteGroup adds a route group to the server
func (s *Server) AddRouteGroup(group RouteGroup) {
	s.routes = append(s.routes, group)
}

// AddRoute adds a single route to the server
func (s *Server) AddRoute(pattern string, handler http.Handler) {
	s.mux.Handle(pattern, handler)
}

// RegisterHealthCheck adds a health check endpoint
func (s *Server) RegisterHealthCheck() {
	s.mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		fmt.Fprint(w, `{"status":"healthy","service":"zephyr-api","timestamp":"`+time.Now().UTC().Format(time.RFC3339)+`"}`)
	})
}

// RegisterMetrics adds a metrics endpoint
func (s *Server) RegisterMetrics() {
	s.mux.HandleFunc("/metrics", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/plain")
		w.WriteHeader(http.StatusOK)
		fmt.Fprint(w, "# Zephyr Weather Service Metrics\n")
		fmt.Fprint(w, "# TYPE zephyr_info gauge\n")
		fmt.Fprint(w, "zephyr_info{version=\"1.0.0-dev\"} 1\n")
	})
}

// buildHandler constructs the final handler with all middleware and routes
func (s *Server) buildHandler() http.Handler {
	// Register all route groups
	for _, group := range s.routes {
		s.mux.Handle(group.Prefix, group.Handler)
	}

	// NOTE: Apply middleware in reverse order (last added = outermost)
	handler := http.Handler(s.mux)
	for i := len(s.middleware) - 1; i >= 0; i-- {
		handler = s.middleware[i](handler)
	}

	return handler
}

// Start starts the HTTP server with graceful shutdown
func (s *Server) Start() error {
	// Build the final handler with middleware
	handler := s.buildHandler()

	// Add CORS support
	corsHandler := s.withCORS(handler)

	// Create HTTP server
	server := &http.Server{
		Addr: fmt.Sprintf("%s:%d", s.config.API.Host, s.config.API.Port),
		// FIXME: Use h2c for HTTP/2 without TLS - Update to TLS in future
		Handler:      h2c.NewHandler(corsHandler, &http2.Server{}),
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
	}

	// Create context for graceful shutdown
	ctx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()

	// Start server in goroutine
	go func() {
		slog.Info("🌤️  Zephyr Weather Service starting",
			"addr", server.Addr,
			"routes", len(s.routes),
			"middleware", len(s.middleware))

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			slog.Error("Server failed to start", "error", err)
		}
	}()

	// Wait for shutdown signal
	<-ctx.Done()

	// Graceful shutdown
	slog.Info("Shutting down server gracefully...")
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(shutdownCtx); err != nil {
		slog.Error("Server forced to shutdown", "error", err)
		return err
	}

	slog.Info("Server shutdown complete")
	return nil
}

// withCORS adds CORS support to the handler
func (s *Server) withCORS(h http.Handler) http.Handler {
	corsOptions := cors.Options{
		AllowedOrigins: []string{
			"http://localhost:3000",
			"http://localhost:3001",
			"http://localhost:8080",
		},
		AllowedMethods:   connectcors.AllowedMethods(),
		AllowedHeaders:   connectcors.AllowedHeaders(),
		ExposedHeaders:   connectcors.ExposedHeaders(),
		AllowCredentials: true,
		MaxAge:           300, // 5 minutes
	}

	// Add development origins if not in production
	if s.config.API.LogLevel == "debug" {
		corsOptions.AllowedOrigins = append(corsOptions.AllowedOrigins, "*")
		corsOptions.AllowCredentials = false
	}

	middleware := cors.New(corsOptions)
	return middleware.Handler(h)
}
