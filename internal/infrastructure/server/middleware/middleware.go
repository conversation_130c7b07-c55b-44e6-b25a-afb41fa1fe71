package middleware

import (
	"log/slog"
	"net/http"
	"time"

	"github.com/emissium/zephyr/internal/infrastructure/utils"
)

// Middleware represents a HTTP middleware function
type Middleware func(http.Handler) http.Handler

// RequestLogger logs HTTP requests with structured logging
func RequestLogger() Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()
			requestID := utils.GenerateRequestID()

			// Add request ID to context and response headers
			r.Header.Set("X-Request-ID", requestID)
			w.Header().Set("X-Request-ID", requestID)

			// Wrap response writer to capture status code
			wrapped := &responseWriter{ResponseWriter: w, statusCode: http.StatusOK}

			// Process request
			next.ServeHTTP(wrapped, r)

			// Log request
			duration := time.Since(start)
			slog.Info("HTTP Request",
				"method", r.Method,
				"path", r.URL.Path,
				"status", wrapped.statusCode,
				"duration", duration,
				"request_id", requestID,
				"user_agent", r.Use<PERSON>(),
				"remote_addr", r.RemoteAddr,
			)
		})
	}
}

// Recovery recovers from panics and returns a 500 error
func Recovery() Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			defer func() {
				if err := recover(); err != nil {
					requestID := r.Header.Get("X-Request-ID")
					slog.Error("Panic recovered",
						"error", err,
						"request_id", requestID,
						"path", r.URL.Path,
						"method", r.Method,
					)

					w.Header().Set("Content-Type", "application/json")
					w.WriteHeader(http.StatusInternalServerError)
					w.Write([]byte(`{"error":"Internal server error","request_id":"` + requestID + `"}`))
				}
			}()
			next.ServeHTTP(w, r)
		})
	}
}

// RateLimiter provides basic rate limiting (placeholder for future implementation)
func RateLimiter(requestsPerMinute int) Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// TODO: Implement rate limiting logic
			// For now, just pass through
			next.ServeHTTP(w, r)
		})
	}
}

// Authentication provides API token authentication
func Authentication(requiredToken string) Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Skip authentication for health and metrics endpoints
			if r.URL.Path == "/health" || r.URL.Path == "/metrics" {
				next.ServeHTTP(w, r)
				return
			}

			// Check for API token
			token := r.Header.Get("Authorization")
			if token == "" {
				token = r.Header.Get("X-API-Key")
			}

			if requiredToken != "" && token != "Bearer "+requiredToken && token != requiredToken {
				requestID := r.Header.Get("X-Request-ID")
				slog.Warn("Unauthorized request",
					"path", r.URL.Path,
					"method", r.Method,
					"request_id", requestID,
				)

				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusUnauthorized)
				w.Write([]byte(`{"error":"Unauthorized","request_id":"` + requestID + `"}`))
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// SecurityHeaders adds security headers to responses
func SecurityHeaders() Middleware {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Add security headers
			w.Header().Set("X-Content-Type-Options", "nosniff")
			w.Header().Set("X-Frame-Options", "DENY")
			w.Header().Set("X-XSS-Protection", "1; mode=block")
			w.Header().Set("Referrer-Policy", "strict-origin-when-cross-origin")
			w.Header().Set("Content-Security-Policy", "default-src 'self'")

			next.ServeHTTP(w, r)
		})
	}
}

// Timeout adds request timeout handling
func Timeout(duration time.Duration) Middleware {
	return func(next http.Handler) http.Handler {
		return http.TimeoutHandler(next, duration, `{"error":"Request timeout"}`)
	}
}

// responseWriter wraps http.ResponseWriter to capture status code
type responseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}

func (rw *responseWriter) Write(b []byte) (int, error) {
	return rw.ResponseWriter.Write(b)
}

func (rw *responseWriter) Header() http.Header {
	return rw.ResponseWriter.Header()
}
