package weather

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	weatherv1 "github.com/emissium/zephyr/api/gen/weather/v1"
	"github.com/emissium/zephyr/internal/ports"
)

// BaseProvider provides common functionality for weather providers
type BaseProvider struct {
	client      *http.Client
	rateLimiter *RateLimiter
	mu          sync.RWMutex
}

// NewBaseProvider creates a new base provider with rate limiting
func NewBaseProvider(requestsPerMinute, requestsPerDay int) *BaseProvider {
	return &BaseProvider{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
		rateLimiter: NewRateLimiter(requestsPerMinute, requestsPerDay),
	}
}

// CheckRateLimit verifies if a request can be made within rate limits
func (b *BaseProvider) CheckRateLimit() error {
	if !b.rateLimiter.Allow() {
		return fmt.Errorf("rate limit exceeded")
	}
	return nil
}

// GetHTTPClient returns the HTTP client for making requests
func (b *BaseProvider) GetHTTPClient() *http.Client {
	b.mu.RLock()
	defer b.mu.RUnlock()
	return b.client
}

// SetHTTPClient sets a custom HTTP client
func (b *BaseProvider) SetHTTPClient(client *http.Client) {
	b.mu.Lock()
	defer b.mu.Unlock()
	b.client = client
}

// GetRateLimit returns the rate limit configuration
func (b *BaseProvider) GetRateLimit() (requestsPerMinute int, requestsPerDay int) {
	return b.rateLimiter.GetLimits()
}

// Verify that BaseProvider implements common provider functionality
var _ ports.WeatherProvider = (*BaseProvider)(nil)

// Default implementations that should be overridden by specific providers
func (b *BaseProvider) FetchCurrent(ctx context.Context, lat, lon float64) (*weatherv1.WeatherDataPoint, error) {
	return nil, fmt.Errorf("FetchCurrent not implemented by provider")
}

func (b *BaseProvider) FetchHistorical(ctx context.Context, lat, lon float64, startDate, endDate time.Time) ([]*weatherv1.WeatherDataPoint, error) {
	return nil, fmt.Errorf("FetchHistorical not implemented by provider")
}

func (b *BaseProvider) FetchForecast(ctx context.Context, lat, lon float64, days int) ([]*weatherv1.WeatherDataPoint, error) {
	return nil, fmt.Errorf("FetchForecast not implemented by provider")
}

func (b *BaseProvider) GetProviderInfo() *weatherv1.DataSourceInfo {
	return &weatherv1.DataSourceInfo{
		Provider:    "base",
		DisplayName: "Base Weather Provider",
		Description: "Base weather provider implementation",
		Active:      false,
	}
}
