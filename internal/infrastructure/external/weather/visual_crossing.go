package weather

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	weatherv1 "github.com/emissium/zephyr/api/gen/weather/v1"
	"github.com/emissium/zephyr/internal/ports"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Visual Crossing API configuration
	VisualCrossingBaseURL = "https://weather.visualcrossing.com/VisualCrossingWebServices/rest/services/timeline"
	MaxHistoricalDays     = 365
	ForecastHours         = 360
)

// VisualCrossingProvider implements the WeatherProvider interface for Visual Crossing API
type VisualCrossingProvider struct {
	*BaseProvider
	apiKey string
}

// NewVisualCrossingProvider creates a new Visual Crossing weather provider
func NewVisualCrossingProvider(apiKey string) *VisualCrossingProvider {
	return &VisualCrossingProvider{
		BaseProvider: NewBaseProvider(100, 1000), // 100 requests/minute, 1000/day
		apiKey:       apiKey,
	}
}

// GetProviderInfo returns information about the Visual Crossing provider
func (vc *VisualCrossingProvider) GetProviderInfo() *weatherv1.DataSourceInfo {
	return &weatherv1.DataSourceInfo{
		Provider:    "visual_crossing",
		DisplayName: "Visual Crossing Weather API",
		Description: "Professional weather data provider with global coverage",
		SupportedDataTypes: []weatherv1.WeatherDataType{
			weatherv1.WeatherDataType_WEATHER_DATA_TYPE_CURRENT,
			weatherv1.WeatherDataType_WEATHER_DATA_TYPE_HISTORICAL,
			weatherv1.WeatherDataType_WEATHER_DATA_TYPE_FORECAST,
		},
		SupportedVariables: []weatherv1.WeatherVariableType{
			weatherv1.WeatherVariableType_WEATHER_VARIABLE_TEMPERATURE,
			weatherv1.WeatherVariableType_WEATHER_VARIABLE_HUMIDITY,
			weatherv1.WeatherVariableType_WEATHER_VARIABLE_PRESSURE,
			weatherv1.WeatherVariableType_WEATHER_VARIABLE_WIND_SPEED,
			weatherv1.WeatherVariableType_WEATHER_VARIABLE_WIND_DIRECTION,
			weatherv1.WeatherVariableType_WEATHER_VARIABLE_VISIBILITY,
			weatherv1.WeatherVariableType_WEATHER_VARIABLE_CLOUD_COVER,
			weatherv1.WeatherVariableType_WEATHER_VARIABLE_UV_INDEX,
		},
		UpdateFrequency:     "15 minutes",
		CoverageDescription: "Global coverage with high accuracy",
		TypicalQuality:      weatherv1.QualityLevel_QUALITY_LEVEL_HIGH,
		Active:              true,
	}
}

// FetchCurrent retrieves current weather data for a location
func (vc *VisualCrossingProvider) FetchCurrent(ctx context.Context, lat, lon float64) (*weatherv1.WeatherDataPoint, error) {
	if err := vc.CheckRateLimit(); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	// Build URL for current weather
	location := fmt.Sprintf("%.6f,%.6f", lat, lon)
	requestURL := fmt.Sprintf("%s/%s", VisualCrossingBaseURL, location)

	// Build query parameters
	params := url.Values{
		"key":         {vc.apiKey},
		"unitGroup":   {"metric"},
		"contentType": {"json"},
		"timezone":    {"Z"},
		"elements":    {"datetime,temp,humidity,pressure,windspeed,winddir,visibility,cloudcover,uvindex"},
		"include":     {"current"},
	}

	// Make request
	resp, err := vc.makeRequest(ctx, requestURL, params)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch current weather: %w", err)
	}

	// Parse response
	dataPoint, err := vc.parseCurrentWeather(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to parse current weather: %w", err)
	}

	return dataPoint, nil
}

// FetchHistorical retrieves historical weather data for a location
func (vc *VisualCrossingProvider) FetchHistorical(ctx context.Context, lat, lon float64, startDate, endDate time.Time) ([]*weatherv1.WeatherDataPoint, error) {
	if err := vc.CheckRateLimit(); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	// Validate time range
	if endDate.Before(startDate) {
		return nil, fmt.Errorf("end date must be after start date")
	}

	days := int(endDate.Sub(startDate).Hours() / 24)
	if days > MaxHistoricalDays {
		return nil, fmt.Errorf("historical time range exceeds maximum of %d days", MaxHistoricalDays)
	}

	// Build URL for historical weather
	location := fmt.Sprintf("%.6f,%.6f", lat, lon)
	dateRange := fmt.Sprintf("%s/%s", startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
	requestURL := fmt.Sprintf("%s/%s/%s", VisualCrossingBaseURL, location, dateRange)

	// Build query parameters
	params := url.Values{
		"key":         {vc.apiKey},
		"unitGroup":   {"metric"},
		"contentType": {"json"},
		"timezone":    {"Z"},
		"elements":    {"datetime,temp,humidity,pressure,windspeed,winddir,visibility,cloudcover,uvindex"},
		"include":     {"hours,days"},
	}

	// Make request
	resp, err := vc.makeRequest(ctx, requestURL, params)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch historical weather: %w", err)
	}

	// Parse response
	dataPoints, err := vc.parseHistoricalWeather(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to parse historical weather: %w", err)
	}

	return dataPoints, nil
}

// FetchForecast retrieves forecast weather data for a location
func (vc *VisualCrossingProvider) FetchForecast(ctx context.Context, lat, lon float64, days int) ([]*weatherv1.WeatherDataPoint, error) {
	if err := vc.CheckRateLimit(); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	if days <= 0 || days > 15 {
		return nil, fmt.Errorf("forecast days must be between 1 and 15")
	}

	// Build URL for forecast weather
	location := fmt.Sprintf("%.6f,%.6f", lat, lon)
	requestURL := fmt.Sprintf("%s/%s", VisualCrossingBaseURL, location)

	// Build query parameters
	params := url.Values{
		"key":         {vc.apiKey},
		"unitGroup":   {"metric"},
		"contentType": {"json"},
		"timezone":    {"Z"},
		"elements":    {"datetime,temp,humidity,pressure,windspeed,winddir,visibility,cloudcover,uvindex"},
		"include":     {"hours,days"},
	}

	// Make request
	resp, err := vc.makeRequest(ctx, requestURL, params)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch forecast weather: %w", err)
	}

	// Parse response
	dataPoints, err := vc.parseForecastWeather(resp, days)
	if err != nil {
		return nil, fmt.Errorf("failed to parse forecast weather: %w", err)
	}

	return dataPoints, nil
}

// makeRequest makes an HTTP request to the Visual Crossing API
func (vc *VisualCrossingProvider) makeRequest(ctx context.Context, requestURL string, params url.Values) (map[string]interface{}, error) {
	// Build full URL with parameters
	fullURL := fmt.Sprintf("%s?%s", requestURL, params.Encode())

	// Create request
	req, err := http.NewRequestWithContext(ctx, "GET", fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Make request
	client := vc.GetHTTPClient()
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	// Check status code
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse JSON response
	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode JSON response: %w", err)
	}

	return result, nil
}

// parseCurrentWeather parses current weather data from Visual Crossing API response
func (vc *VisualCrossingProvider) parseCurrentWeather(data map[string]interface{}) (*weatherv1.WeatherDataPoint, error) {
	current, ok := data["currentConditions"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("no current conditions found in response")
	}

	// Parse timestamp
	datetimeStr, ok := current["datetime"].(string)
	if !ok {
		return nil, fmt.Errorf("no datetime found in current conditions")
	}

	timestamp, err := vc.parseTimestamp(datetimeStr)
	if err != nil {
		return nil, fmt.Errorf("failed to parse timestamp: %w", err)
	}

	// Create data point
	dataPoint := &weatherv1.WeatherDataPoint{
		Timestamp:    timestamp,
		DataType:     weatherv1.WeatherDataType_WEATHER_DATA_TYPE_CURRENT,
		Measurements: []*weatherv1.WeatherMeasurement{},
	}

	// Parse measurements
	measurements := vc.parseWeatherMeasurements(current)
	dataPoint.Measurements = measurements

	return dataPoint, nil
}

// parseHistoricalWeather parses historical weather data from Visual Crossing API response
func (vc *VisualCrossingProvider) parseHistoricalWeather(data map[string]interface{}) ([]*weatherv1.WeatherDataPoint, error) {
	days, ok := data["days"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("no days found in response")
	}

	var dataPoints []*weatherv1.WeatherDataPoint

	for _, dayData := range days {
		day, ok := dayData.(map[string]interface{})
		if !ok {
			continue
		}

		// Parse hours if available
		if hours, ok := day["hours"].([]interface{}); ok {
			for _, hourData := range hours {
				hour, ok := hourData.(map[string]interface{})
				if !ok {
					continue
				}

				dataPoint, err := vc.parseHourlyDataPoint(hour)
				if err != nil {
					continue // Skip invalid data points
				}

				dataPoints = append(dataPoints, dataPoint)
			}
		}
	}

	return dataPoints, nil
}

// parseForecastWeather parses forecast weather data from Visual Crossing API response
func (vc *VisualCrossingProvider) parseForecastWeather(data map[string]interface{}, days int) ([]*weatherv1.WeatherDataPoint, error) {
	daysData, ok := data["days"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("no days found in response")
	}

	var dataPoints []*weatherv1.WeatherDataPoint
	processedDays := 0

	for _, dayData := range daysData {
		if processedDays >= days {
			break
		}

		day, ok := dayData.(map[string]interface{})
		if !ok {
			continue
		}

		// Parse hours if available
		if hours, ok := day["hours"].([]interface{}); ok {
			for _, hourData := range hours {
				hour, ok := hourData.(map[string]interface{})
				if !ok {
					continue
				}

				dataPoint, err := vc.parseHourlyDataPoint(hour)
				if err != nil {
					continue // Skip invalid data points
				}

				dataPoint.DataType = weatherv1.WeatherDataType_WEATHER_DATA_TYPE_FORECAST
				dataPoints = append(dataPoints, dataPoint)
			}
		}

		processedDays++
	}

	return dataPoints, nil
}

// parseHourlyDataPoint parses a single hourly data point
func (vc *VisualCrossingProvider) parseHourlyDataPoint(hour map[string]interface{}) (*weatherv1.WeatherDataPoint, error) {
	// Parse timestamp
	datetimeStr, ok := hour["datetime"].(string)
	if !ok {
		return nil, fmt.Errorf("no datetime found in hour data")
	}

	timestamp, err := vc.parseTimestamp(datetimeStr)
	if err != nil {
		return nil, fmt.Errorf("failed to parse timestamp: %w", err)
	}

	// Create data point
	dataPoint := &weatherv1.WeatherDataPoint{
		Timestamp:    timestamp,
		DataType:     weatherv1.WeatherDataType_WEATHER_DATA_TYPE_HISTORICAL,
		Measurements: []*weatherv1.WeatherMeasurement{},
	}

	// Parse measurements
	measurements := vc.parseWeatherMeasurements(hour)
	dataPoint.Measurements = measurements

	return dataPoint, nil
}

// parseTimestamp parses a timestamp string from Visual Crossing API
func (vc *VisualCrossingProvider) parseTimestamp(datetimeStr string) (*timestamppb.Timestamp, error) {
	// Visual Crossing returns timestamps in various formats
	// Try common formats
	formats := []string{
		"2006-01-02T15:04:05",
		"2006-01-02 15:04:05",
		"15:04:05",
		"2006-01-02",
	}

	var parsedTime time.Time
	var err error

	for _, format := range formats {
		parsedTime, err = time.Parse(format, datetimeStr)
		if err == nil {
			break
		}
	}

	if err != nil {
		return nil, fmt.Errorf("failed to parse timestamp %s: %w", datetimeStr, err)
	}

	return timestamppb.New(parsedTime), nil
}

// parseWeatherMeasurements parses weather measurements from Visual Crossing data
func (vc *VisualCrossingProvider) parseWeatherMeasurements(data map[string]interface{}) []*weatherv1.WeatherMeasurement {
	var measurements []*weatherv1.WeatherMeasurement

	// Temperature
	if temp, ok := data["temp"].(float64); ok {
		measurements = append(measurements, &weatherv1.WeatherMeasurement{
			VariableType: weatherv1.WeatherVariableType_WEATHER_VARIABLE_TEMPERATURE,
			Value: &weatherv1.WeatherValue{
				Value: &weatherv1.WeatherValue_NumericValue{NumericValue: temp},
			},
			Unit: "°C",
			Source: &weatherv1.DataSource{
				Provider:    "visual_crossing",
				CollectedAt: timestamppb.Now(),
			},
			Quality: &weatherv1.DataQuality{
				ConfidenceScore: 0.9,
				Level:           weatherv1.QualityLevel_QUALITY_LEVEL_HIGH,
			},
		})
	}

	// Humidity
	if humidity, ok := data["humidity"].(float64); ok {
		measurements = append(measurements, &weatherv1.WeatherMeasurement{
			VariableType: weatherv1.WeatherVariableType_WEATHER_VARIABLE_HUMIDITY,
			Value: &weatherv1.WeatherValue{
				Value: &weatherv1.WeatherValue_NumericValue{NumericValue: humidity},
			},
			Unit: "%",
			Source: &weatherv1.DataSource{
				Provider:    "visual_crossing",
				CollectedAt: timestamppb.Now(),
			},
			Quality: &weatherv1.DataQuality{
				ConfidenceScore: 0.9,
				Level:           weatherv1.QualityLevel_QUALITY_LEVEL_HIGH,
			},
		})
	}

	// Pressure
	if pressure, ok := data["pressure"].(float64); ok {
		measurements = append(measurements, &weatherv1.WeatherMeasurement{
			VariableType: weatherv1.WeatherVariableType_WEATHER_VARIABLE_PRESSURE,
			Value: &weatherv1.WeatherValue{
				Value: &weatherv1.WeatherValue_NumericValue{NumericValue: pressure},
			},
			Unit: "hPa",
			Source: &weatherv1.DataSource{
				Provider:    "visual_crossing",
				CollectedAt: timestamppb.Now(),
			},
			Quality: &weatherv1.DataQuality{
				ConfidenceScore: 0.9,
				Level:           weatherv1.QualityLevel_QUALITY_LEVEL_HIGH,
			},
		})
	}

	// Wind Speed
	if windSpeed, ok := data["windspeed"].(float64); ok {
		measurements = append(measurements, &weatherv1.WeatherMeasurement{
			VariableType: weatherv1.WeatherVariableType_WEATHER_VARIABLE_WIND_SPEED,
			Value: &weatherv1.WeatherValue{
				Value: &weatherv1.WeatherValue_NumericValue{NumericValue: windSpeed},
			},
			Unit: "m/s",
			Source: &weatherv1.DataSource{
				Provider:    "visual_crossing",
				CollectedAt: timestamppb.Now(),
			},
			Quality: &weatherv1.DataQuality{
				ConfidenceScore: 0.9,
				Level:           weatherv1.QualityLevel_QUALITY_LEVEL_HIGH,
			},
		})
	}

	// Wind Direction
	if windDir, ok := data["winddir"].(float64); ok {
		measurements = append(measurements, &weatherv1.WeatherMeasurement{
			VariableType: weatherv1.WeatherVariableType_WEATHER_VARIABLE_WIND_DIRECTION,
			Value: &weatherv1.WeatherValue{
				Value: &weatherv1.WeatherValue_NumericValue{NumericValue: windDir},
			},
			Unit: "degrees",
			Source: &weatherv1.DataSource{
				Provider:    "visual_crossing",
				CollectedAt: timestamppb.Now(),
			},
			Quality: &weatherv1.DataQuality{
				ConfidenceScore: 0.9,
				Level:           weatherv1.QualityLevel_QUALITY_LEVEL_HIGH,
			},
		})
	}

	return measurements
}

// Verify that VisualCrossingProvider implements WeatherProvider interface
var _ ports.WeatherProvider = (*VisualCrossingProvider)(nil)
