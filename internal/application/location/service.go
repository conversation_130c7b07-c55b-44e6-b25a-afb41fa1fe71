package location

import (
	"context"

	"connectrpc.com/connect"
	errbuilder "github.com/ZanzyTHEbar/errbuilder-go"
	locationv1 "github.com/emissium/zephyr/api/gen/location/v1"
	"github.com/emissium/zephyr/api/gen/location/v1/locationv1connect"
	locationdomain "github.com/emissium/zephyr/internal/domain/location"
	"google.golang.org/protobuf/types/known/emptypb"
)

// Service implements the LocationService ConnectRPC interface
type Service struct {
	normalizer *locationdomain.Normalizer
	// TODO: Add repository interfaces for data persistence
	// locationRepo ports.LocationRepository
	// logger       logger.Logger
}

// NewService creates a new location service instance
func NewService(precision int) *Service {
	return &Service{
		normalizer: locationdomain.NewNormalizer(precision),
	}
}

// Verify that Service implements the LocationServiceHandler interface
var _ locationv1connect.LocationServiceHandler = (*Service)(nil)

// CreateLocation registers a new location for weather monitoring
func (s *Service) CreateLocation(
	ctx context.Context,
	req *connect.Request[locationv1.CreateLocationRequest],
) (*connect.Response[locationv1.Location], error) {
	// Use our normalizer to create and validate the location
	location, err := s.normalizer.CreateLocation(req.Msg)
	if err != nil {
		return nil, connect.NewError(connect.CodeInvalidArgument, err)
	}

	// TODO: Persist location to repository
	// if err := s.locationRepo.Save(ctx, location); err != nil {
	//     return nil, connect.NewError(connect.CodeInternal, err)
	// }

	// TODO: Trigger initial weather fetch
	// s.triggerWeatherFetch(ctx, location)

	return connect.NewResponse(location), nil
}

// GetLocation retrieves a specific location by ID
func (s *Service) GetLocation(
	ctx context.Context,
	req *connect.Request[locationv1.GetLocationRequest],
) (*connect.Response[locationv1.Location], error) {
	if req.Msg.Id == "" {
		return nil, connect.NewError(
			connect.CodeInvalidArgument,
			errbuilder.NewErrBuilder().
				WithLabel("validation_error").
				WithMsg("location ID cannot be empty"),
		)
	}

	// TODO: Implement repository lookup
	// location, err := s.locationRepo.GetByID(ctx, req.Msg.Id)
	// if err != nil {
	//     if errors.Is(err, ports.ErrLocationNotFound) {
	//         return nil, connect.NewError(connect.CodeNotFound, err)
	//     }
	//     return nil, connect.NewError(connect.CodeInternal, err)
	// }

	// Placeholder implementation
	return nil, connect.NewError(
		connect.CodeUnimplemented,
		errbuilder.NewErrBuilder().
			WithLabel("not_implemented").
			WithMsg("GetLocation not yet implemented"),
	)
}

// ListActiveLocations returns all currently active locations
func (s *Service) ListActiveLocations(
	ctx context.Context,
	req *connect.Request[locationv1.ListActiveLocationsRequest],
) (*connect.Response[locationv1.LocationList], error) {
	// TODO: Implement repository query
	// locations, err := s.locationRepo.ListActive(ctx, &ports.ListOptions{
	//     Types:  req.Msg.Types,
	//     Offset: int(req.Msg.Offset),
	//     Limit:  int(req.Msg.Limit),
	// })
	// if err != nil {
	//     return nil, connect.NewError(connect.CodeInternal, err)
	// }

	// Placeholder implementation
	return nil, connect.NewError(
		connect.CodeUnimplemented,
		errbuilder.NewErrBuilder().
			WithLabel("not_implemented").
			WithMsg("ListActiveLocations not yet implemented"),
	)
}

// UpdateLocationTypes updates the types associated with a location
func (s *Service) UpdateLocationTypes(
	ctx context.Context,
	req *connect.Request[locationv1.UpdateLocationTypesRequest],
) (*connect.Response[locationv1.Location], error) {
	if req.Msg.Id == "" {
		return nil, connect.NewError(
			connect.CodeInvalidArgument,
			errbuilder.NewErrBuilder().
				WithLabel("validation_error").
				WithMsg("location ID cannot be empty"),
		)
	}

	if len(req.Msg.Types) == 0 {
		return nil, connect.NewError(
			connect.CodeInvalidArgument,
			errbuilder.NewErrBuilder().
				WithLabel("validation_error").
				WithMsg("location types cannot be empty"),
		)
	}

	// TODO: Implement repository update
	// location, err := s.locationRepo.UpdateTypes(ctx, req.Msg.Id, req.Msg.Types)
	// if err != nil {
	//     if errors.Is(err, ports.ErrLocationNotFound) {
	//         return nil, connect.NewError(connect.CodeNotFound, err)
	//     }
	//     return nil, connect.NewError(connect.CodeInternal, err)
	// }

	// Placeholder implementation
	return nil, connect.NewError(
		connect.CodeUnimplemented,
		errbuilder.NewErrBuilder().
			WithLabel("not_implemented").
			WithMsg("UpdateLocationTypes not yet implemented"),
	)
}

// DeactivateLocation removes a location from active monitoring
func (s *Service) DeactivateLocation(
	ctx context.Context,
	req *connect.Request[locationv1.DeactivateLocationRequest],
) (*connect.Response[emptypb.Empty], error) {
	if req.Msg.Id == "" {
		return nil, connect.NewError(
			connect.CodeInvalidArgument,
			errbuilder.NewErrBuilder().
				WithLabel("validation_error").
				WithMsg("location ID cannot be empty"),
		)
	}

	// TODO: Implement repository deactivation
	// if err := s.locationRepo.Deactivate(ctx, req.Msg.Id); err != nil {
	//     if errors.Is(err, ports.ErrLocationNotFound) {
	//         return nil, connect.NewError(connect.CodeNotFound, err)
	//     }
	//     return nil, connect.NewError(connect.CodeInternal, err)
	// }

	// Placeholder implementation
	return nil, connect.NewError(
		connect.CodeUnimplemented,
		errbuilder.NewErrBuilder().
			WithLabel("not_implemented").
			WithMsg("DeactivateLocation not yet implemented"),
	)
}
