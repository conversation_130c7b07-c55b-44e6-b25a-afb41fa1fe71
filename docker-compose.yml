version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - REDIS_HOST=redis
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=${INFLUXDB_TOKEN}
      - INFLUXDB_ORG=${INFLUXDB_ORG}
      - INFLUXDB_BUCKET=${INFLUXDB_BUCKET}
      - API_TOKEN=${API_TOKEN:-your-super-secret-token}
      - DOCS_USERNAME=${DOCS_USERNAME:-admin}
      - DOCS_PASSWORD=${DOCS_PASSWORD:-admin}
    depends_on:
      redis:
        condition: service_healthy
      influxdb:
        condition: service_healthy
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    restart: unless-stopped

  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    environment:
      - REDIS_HOST=redis
      - INFLUXDB_URL=http://influxdb:8086
      - INFLUXDB_TOKEN=${INFLUXDB_TOKEN}
      - INFLUXDB_ORG=${INFLUXDB_ORG}
      - INFLUXDB_BUCKET=${INFLUXDB_BUCKET}
      - VISUAL_CROSSING_API_KEY=${VISUAL_CROSSING_API_KEY}
      - VISUAL_CROSSING_BASE_URL=${VISUAL_CROSSING_BASE_URL}
      - COORDINATE_PRECISION=${COORDINATE_PRECISION}
      - POLLING_INTERVAL=${POLLING_INTERVAL:-900}
      - INACTIVE_CLEANUP_DAYS=${INACTIVE_CLEANUP_DAYS:-30}
    depends_on:
      redis:
        condition: service_healthy
      influxdb:
        condition: service_healthy
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    restart: unless-stopped

  redis:
    image: redis:7.2-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    restart: unless-stopped

  influxdb:
    image: influxdb:2.7.10-alpine
    ports:
      - "8086:8086"
    volumes:
      - influxdb_data:/var/lib/influxdb2
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=adminpassword
      - DOCKER_INFLUXDB_INIT_ORG=zephyr
      - DOCKER_INFLUXDB_INIT_BUCKET=weather_data
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=your-super-secret-token
      - INFLUXDB_TOKEN=${INFLUXDB_TOKEN}
      - INFLUXDB_ORG=${INFLUXDB_ORG}
      - INFLUXDB_BUCKET=${INFLUXDB_BUCKET}
    healthcheck:
      test: ["CMD", "influx", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    restart: unless-stopped

volumes:
  redis_data:
  influxdb_data: