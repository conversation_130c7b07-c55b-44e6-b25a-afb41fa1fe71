# Copyright © 2025 Emissium
# This file is part of the Emissium library.

FROM python:3.12-slim

# add labels for better maintainability
LABEL maintainer="Emissium"
LABEL description="Zephyr Weather Service Worker"

WORKDIR /app

# create a non-root user (if it doesn't exist)
RUN groupadd -r zephyr || true && \
    useradd -r -g zephyr zephyr || true

# copy requirements first to leverage Docker cache
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# copy application code
COPY . .

# change ownership of the app directory
RUN chown -R zephyr:zephyr /app

# switch to non-root user
USER zephyr

# add health check that verifies redis and influxdb connections
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD python -c "import redis, os; r=redis.Redis(host=os.environ['REDIS_HOST']); r.ping() == True or exit(1)"

# run the worker
CMD ["python", "-m", "app.workers.scheduler"]