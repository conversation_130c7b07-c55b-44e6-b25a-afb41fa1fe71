syntax = "proto3";

package location.v1;

option go_package = "github.com/emissium/zephyr/api/gen/location/v1;locationv1";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

// LocationService provides location management functionality
service LocationService {
  // CreateLocation registers a new location for weather monitoring
  rpc CreateLocation(CreateLocationRequest) returns (Location);

  // GetLocation retrieves a specific location by ID
  rpc GetLocation(GetLocationRequest) returns (Location);

  // ListActiveLocations returns all currently active locations
  rpc ListActiveLocations(ListActiveLocationsRequest) returns (LocationList);

  // UpdateLocationTypes updates the types associated with a location
  rpc UpdateLocationTypes(UpdateLocationTypesRequest) returns (Location);

  // DeactivateLocation removes a location from active monitoring
  rpc DeactivateLocation(DeactivateLocationRequest)
      returns (google.protobuf.Empty);
}

// Location represents a geographical point for weather monitoring
message Location {
  // Unique identifier (format: "lat:lon" after normalization)
  string id = 1;

  // Original latitude provided by user
  double latitude = 2;

  // Original longitude provided by user
  double longitude = 3;

  // Normalized latitude (rounded to configured precision)
  double normalized_lat = 4;

  // Normalized longitude (rounded to configured precision)
  double normalized_lon = 5;

  // Location types determining which weather variables to collect
  repeated string types = 6;

  // Last time weather data was polled for this location
  optional google.protobuf.Timestamp last_polled = 7;

  // When this location was first registered
  google.protobuf.Timestamp created_at = 8;

  // Whether this location is actively being monitored
  bool active = 9;
}

// CreateLocationRequest contains data for registering a new location
message CreateLocationRequest {
  // Latitude coordinate
  double latitude = 1;

  // Longitude coordinate
  double longitude = 2;

  // Location types (e.g., "solar", "wind_onshore", "consumption")
  repeated string types = 3;
}

// GetLocationRequest identifies a location to retrieve
message GetLocationRequest {
  // Location identifier
  string id = 1;
}

// ListActiveLocationsRequest filters for listing locations
message ListActiveLocationsRequest {
  // Optional filter by location types
  repeated string types = 1;

  // Pagination offset
  int32 offset = 2;

  // Pagination limit (max 100)
  int32 limit = 3;
}

// LocationList contains multiple locations
message LocationList {
  // Array of locations
  repeated Location locations = 1;

  // Total count for pagination
  int32 total = 2;
}

// UpdateLocationTypesRequest updates types for an existing location
message UpdateLocationTypesRequest {
  // Location identifier
  string id = 1;

  // New location types
  repeated string types = 2;
}

// DeactivateLocationRequest identifies a location to deactivate
message DeactivateLocationRequest {
  // Location identifier
  string id = 1;
}
