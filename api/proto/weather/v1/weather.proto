syntax = "proto3";

package weather.v1;

option go_package = "github.com/emissium/zephyr/api/gen/weather/v1;weatherv1";

import "google/protobuf/timestamp.proto";

// WeatherService provides weather data access functionality
service WeatherService {
  // GetCurrentWeather retrieves the latest weather data for a location
  rpc GetCurrentWeather(GetWeatherRequest) returns (WeatherResponse);

  // GetHistoricalWeather retrieves past weather data for a location
  rpc GetHistoricalWeather(GetHistoricalWeatherRequest)
      returns (WeatherResponse);

  // GetForecastWeather retrieves weather forecast data for a location
  rpc GetForecastWeather(GetForecastWeatherRequest) returns (WeatherResponse);

  // GetWeatherVariables returns metadata about available weather variables
  rpc GetWeatherVariables(GetWeatherVariablesRequest)
      returns (WeatherVariablesResponse);
}

// WeatherResponse contains weather data for a location
message WeatherResponse {
  // Location identifier
  string location_id = 1;

  // Weather data points
  repeated WeatherData data = 2;

  // Data source provider
  string provider = 3;

  // When this data was last updated
  google.protobuf.Timestamp updated_at = 4;
}

// WeatherData represents weather conditions at a specific time
message WeatherData {
  // Timestamp for this weather observation/forecast
  google.protobuf.Timestamp timestamp = 1;

  // Weather indicator values (e.g., "temperature": 25.5)
  map<string, WeatherValue> indicators = 2;
}

// WeatherValue can hold different types of weather data
message WeatherValue {
  oneof value {
    double numeric_value = 1;
    string string_value = 2;
    bool boolean_value = 3;
  }

  // Unit of measurement (e.g., "°C", "m/s", "%")
  string unit = 4;
}

// GetWeatherRequest identifies a location for current weather
message GetWeatherRequest {
  // Location identifier
  string location_id = 1;

  // Optional filter for specific weather variables
  repeated string variables = 2;
}

// GetHistoricalWeatherRequest specifies historical data parameters
message GetHistoricalWeatherRequest {
  // Location identifier
  string location_id = 1;

  // Start of time range
  google.protobuf.Timestamp start_time = 2;

  // End of time range
  google.protobuf.Timestamp end_time = 3;

  // Optional filter for specific weather variables
  repeated string variables = 4;

  // Data aggregation interval (e.g., "1h", "1d")
  string interval = 5;
}

// GetForecastWeatherRequest specifies forecast data parameters
message GetForecastWeatherRequest {
  // Location identifier
  string location_id = 1;

  // How far into the future to forecast
  string forecast_period = 2;

  // Optional filter for specific weather variables
  repeated string variables = 3;
}

// GetWeatherVariablesRequest filters for variable metadata
message GetWeatherVariablesRequest {
  // Optional filter by location types
  repeated string location_types = 1;
}

// WeatherVariablesResponse contains weather variable metadata
message WeatherVariablesResponse {
  // Available weather variables
  repeated WeatherVariable variables = 1;
}

// WeatherVariable describes a weather measurement
message WeatherVariable {
  // Variable identifier (e.g., "temperature")
  string name = 1;

  // Human-readable description
  string description = 2;

  // Unit of measurement
  string unit = 3;

  // Location types that use this variable
  repeated string location_types = 4;

  // Data type (numeric, string, boolean)
  string data_type = 5;

  // Whether this variable is available in current conditions
  bool current_available = 6;

  // Whether this variable is available in historical data
  bool historical_available = 7;

  // Whether this variable is available in forecasts
  bool forecast_available = 8;
}
